module Madmin
  module ApplicationHelper
    include Pagy::Frontend
    include Rails.application.routes.url_helpers

    def clear_search_params
      # Generate the correct namespaced index path for clearing search
      model_name = resource.model.model_name.route_key
      Rails.application.routes.url_helpers.send(
        "super_admin_madmin_#{model_name}_path", 
        sort: params[:sort], 
        direction: params[:direction]
      )
    end
  end
end
