class RoleResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user_roles
  attribute :users

  # Add scopes to easily filter records
  # scope :published

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Users with Role", madmin_users_path(scope: record.name),
            class: "btn btn-info"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record) = record.name

  # Customize the default sort column and direction.
  def self.default_sort_column = "name"
  def self.default_sort_direction = "asc"
end
