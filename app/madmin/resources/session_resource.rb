class SessionResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :user_agent
  attribute :ip_address
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user

  # Add scopes to easily filter records
  scope :recent
  scope :active
  scope :mobile
  scope :desktop

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View User", madmin_user_path(record.user), class: "btn btn-info"
  end

  member_action do |record|
    link_to "Terminate Session", "#", class: "btn btn-danger",
            data: { confirm: "Are you sure you want to terminate this session?" }
  end

  member_action do |record|
    link_to "View User's Sessions", madmin_sessions_path(user_id: record.user.id),
            class: "btn btn-secondary"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    user_name = record.user.full_name || record.user.email
    device = record.user_agent.include?('Mobile') ? 'Mobile' : 'Desktop'
    "#{user_name} (#{device}) - #{record.ip_address}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "updated_at"
  def self.default_sort_direction = "desc"
end
