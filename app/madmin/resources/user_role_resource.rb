class UserRoleResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user
  attribute :role

  # Add scopes to easily filter records by role type
  scope :superadmin
  scope :admin
  scope :support

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View User", madmin_user_path(record.user), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Role", madmin_role_path(record.role), class: "btn btn-secondary"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    "#{record.user.full_name || record.user.email} - #{record.role.name}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
