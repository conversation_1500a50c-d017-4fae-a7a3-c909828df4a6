class ConversationResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :job
  attribute :conversation_participants
  attribute :users
  attribute :messages

  # Add scopes to easily filter records
  scope :with_job
  scope :without_job
  scope :recent
  scope :active

  # Add actions to the resource's show page
  member_action do |record|
    if record.job
      link_to "View Job", madmin_job_path(record.job), class: "btn btn-info"
    end
  end

  member_action do |record|
    link_to "View Messages (#{record.messages.count})",
            madmin_messages_path(conversation_id: record.id),
            class: "btn btn-secondary"
  end

  member_action do |record|
    link_to "View Participants (#{record.users.count})",
            madmin_conversation_participants_path(conversation_id: record.id),
            class: "btn btn-outline-info"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    participants = record.users.map { |u| u.full_name || u.email }.join(", ")
    job_info = record.job ? " (#{record.job.title})" : ""
    "#{participants}#{job_info}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "updated_at"
  def self.default_sort_direction = "desc"
end
