class JobResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :job_category
  attribute :title
  attribute :description
  attribute :notification_preference
  attribute :outcome
  attribute :platform
  attribute :payment_frequency
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :status
  attribute :application_deadline
  attribute :client_count, form: false
  attribute :charge_per_client
  attribute :business_challenge
  attribute :useful_info
  attribute :offer_summary
  attribute :budget_range
  attribute :topics
  attribute :expires_at
  attribute :is_premium
  attribute :published_at
  attribute :newsletter_frequency
  attribute :newsletter_length
  attribute :lead_magnet_type
  attribute :work_duration
  attribute :target_audience_description
  attribute :emulated_brands_description
  attribute :involvement_level
  attribute :social_media_goal_type
  attribute :social_media_understands_risk_acknowledged
  attribute :requirements
  attribute :headline
  attribute :show_headline_in_form

  # Associations
  attribute :organization
  attribute :job_applications
  attribute :applicants
  attribute :saved_jobs
  attribute :saved_by_users

  # Add scopes to easily filter records
  scope :published
  scope :draft
  scope :expired
  scope :premium
  scope :newsletter_jobs
  scope :social_media_jobs
  scope :lead_magnet_jobs

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Applications (#{record.job_applications.count})",
            madmin_job_applications_path(job_id: record.id),
            class: "btn btn-info"
  end

  member_action do |record|
    if record.status == 'published'
      link_to "Mark as Draft", "#", class: "btn btn-warning"
    else
      link_to "Publish Job", "#", class: "btn btn-success"
    end
  end

  member_action do |record|
    link_to "View Organization", madmin_organization_path(record.organization),
            class: "btn btn-secondary"
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record) = record.title

  # Customize the default sort column and direction.
  def self.default_sort_column = "created_at"
  def self.default_sort_direction = "desc"
end
