class JobInvitationResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :invitation_letter
  attribute :invited_at
  attribute :status
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :accepted_at
  attribute :ignored_at

  # Associations
  attribute :job
  attribute :user
  attribute :talent_profile
  attribute :job_application

  # Add scopes to easily filter records
  scope :pending
  scope :accepted
  scope :ignored
  scope :recent

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Job", madmin_job_path(record.job), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Talent", madmin_user_path(record.user), class: "btn btn-secondary"
  end

  member_action do |record|
    if record.talent_profile
      link_to "View Talent Profile", madmin_talent_profile_path(record.talent_profile),
              class: "btn btn-outline-info"
    end
  end

  member_action do |record|
    if record.job_application
      link_to "View Application", madmin_job_application_path(record.job_application),
              class: "btn btn-outline-success"
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    "#{record.user.full_name || record.user.email} ← #{record.job.title}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "invited_at"
  def self.default_sort_direction = "desc"
end
