class JobApplicationResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :status
  attribute :applied_at
  attribute :accepted_at
  attribute :rejected_at
  attribute :application_letter
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :additional_info
  attribute :salary_considered
  attribute :invited
  attribute :resume, index: false
  attribute :documents, index: false

  # Associations
  attribute :job
  attribute :user
  attribute :job_invitation

  # Add scopes to easily filter records
  scope :pending
  scope :accepted
  scope :rejected
  scope :invited
  scope :recent

  # Add actions to the resource's show page
  member_action do |record|
    link_to "View Job", madmin_job_path(record.job), class: "btn btn-info"
  end

  member_action do |record|
    link_to "View Applicant", madmin_user_path(record.user), class: "btn btn-secondary"
  end

  member_action do |record|
    if record.status == 'pending'
      link_to "Accept Application", "#", class: "btn btn-success"
    end
  end

  member_action do |record|
    if record.status == 'pending'
      link_to "Reject Application", "#", class: "btn btn-danger"
    end
  end

  # Customize the display name of records in the admin area.
  def self.display_name(record)
    "#{record.user.full_name || record.user.email} → #{record.job.title}"
  end

  # Customize the default sort column and direction.
  def self.default_sort_column = "applied_at"
  def self.default_sort_direction = "desc"
end
