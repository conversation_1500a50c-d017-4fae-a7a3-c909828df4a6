class SuperAdmin::AdminBaseController < SuperAdmin::BaseController
  before_action :set_page_size
  before_action :set_search_params

  protected

  def set_page_size
    @page_size = params[:page_size]&.to_i || 25
    @page_size = 100 if @page_size > 100 # Cap at 100
  end

  def set_search_params
    @search = params[:search]
    @sort_by = params[:sort_by]
    @sort_direction = params[:sort_direction] == 'desc' ? 'desc' : 'asc'
  end

  def paginate_collection(collection)
    collection.page(params[:page]).per(@page_size)
  end

  def apply_search(collection, searchable_fields)
    return collection unless @search.present?

    search_conditions = searchable_fields.map do |field|
      "#{field} ILIKE ?"
    end.join(' OR ')

    search_values = Array.new(searchable_fields.length, "%#{@search}%")
    collection.where(search_conditions, *search_values)
  end

  def apply_sorting(collection, sortable_fields)
    return collection unless @sort_by.present? && sortable_fields.include?(@sort_by)

    collection.order("#{@sort_by} #{@sort_direction}")
  end

  def admin_breadcrumbs
    [
      { name: 'Dashboard', path: super_admin_root_path },
      { name: 'Admin Interface', path: super_admin_admin_users_path }
    ]
  end
end
