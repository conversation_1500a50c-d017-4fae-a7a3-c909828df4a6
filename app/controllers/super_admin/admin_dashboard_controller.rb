class SuperAdmin::AdminDashboardController < SuperAdmin::AdminBaseController
  def index
    @stats = {
      users: {
        total: User.count,
        verified: User.where(verified: true).count,
        scouts: User.joins(:roles).where(roles: { name: 'scout' }).count,
        talents: User.joins(:roles).where(roles: { name: 'talent' }).count
      },
      jobs: {
        total: Job.count,
        published: Job.published.count,
        draft: Job.draft.count,
        expired: Job.expired.count
      },
      organizations: {
        total: Organization.count,
        with_jobs: Organization.joins(:jobs).distinct.count
      },
      communication: {
        chat_requests: ChatRequest.count,
        conversations: Conversation.count,
        messages: Message.count
      },
      applications: {
        total: JobApplication.count,
        pending: JobApplication.where(status: 'pending').count,
        accepted: JobApplication.where(status: 'accepted').count
      }
    }

    @recent_activity = {
      recent_users: User.order(created_at: :desc).limit(5),
      recent_jobs: Job.order(created_at: :desc).limit(5),
      recent_chat_requests: ChatRequest.includes(:scout, :talent).order(created_at: :desc).limit(5)
    }
  end
end
