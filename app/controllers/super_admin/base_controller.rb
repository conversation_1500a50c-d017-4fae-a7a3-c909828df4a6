module SuperAdmin
  class BaseController < ApplicationController
    layout 'super_admin'

    before_action :set_current_request_details
    before_action :authenticate
    before_action :check_verification
    before_action :require_onboarding_completion
    before_action :set_current_organization
    before_action :require_organization_selected
    before_action :require_super_admin_access

    private

    def require_super_admin_access
      unless Current.user&.superadmin?
        redirect_to root_path, alert: 'Access denied. Super admin privileges required.'
      end
    end
  end
end
