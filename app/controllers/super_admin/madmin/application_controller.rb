module SuperAdmin
  module Madmin
    class ApplicationController < SuperAdmin::BaseController
      include Rails.application.routes.url_helpers
      if defined?(Madmin::BaseController::InstanceMethods)
        include Madmin::BaseController::InstanceMethods
      end

      # Include Madmin specific functionality
      include ::ActiveStorage::SetCurrent if defined?(::ActiveStorage)
      include Pagy::Backend

      protect_from_forgery with: :exception

      def current_user
        Current.user
      end
    end
  end
end
