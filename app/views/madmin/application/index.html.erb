<%# Custom madmin index view override for namespace compatibility %>
<%= content_for :title, resource.friendly_name.pluralize %>

<header class="header">
  <h1><%= resource.friendly_name.pluralize %></h1>

  <div class="actions">
    <form class="search">
      <%= hidden_field_tag :page, params[:page], value: 1 %>
      <%= search_field_tag :q, params[:q], placeholder: "Search" %>
    </form>

    <%= link_to clear_search_params, class: "btn btn-secondary" do %>
      <svg xmlns="http://www.w3.org/2000/svg" height="1rem" width="1rem" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    <% end if params[:q].present? %>

    <%
    # Generate the correct namespaced new path
    model_name = resource.model.model_name.singular_route_key
    new_path = Rails.application.routes.url_helpers.send("new_super_admin_madmin_#{model_name}_path")
    %>
    <%= link_to "New #{resource.friendly_name}", new_path, class: "btn btn-secondary" %>
  </div>
</header>

<nav class="scopes">
  <% if resource.scopes.any? %>
    <%
    # Generate the correct namespaced index path for scopes
    model_name = resource.model.model_name.route_key
    index_path = Rails.application.routes.url_helpers.send("super_admin_madmin_#{model_name}_path")
    %>
    <%= link_to "All", index_path, class: class_names("btn btn-secondary", {"active" => params[:scope].blank?}) %>
  <% end %>

  <% resource.scopes.each do |scope| %>
    <%
    # Generate the correct namespaced index path for each scope
    model_name = resource.model.model_name.route_key
    scope_path = Rails.application.routes.url_helpers.send("super_admin_madmin_#{model_name}_path", scope: scope, q: params[:q], sort: params[:sort], direction: params[:direction])
    %>
    <%= link_to scope.to_s.humanize, scope_path, class: class_names("btn btn-secondary", {"active" => params[:scope] == scope.to_s}) %>
  <% end %>
</nav>

<div class="table-scroll">
  <table>
    <thead>
      <tr>
        <% resource.attributes.values.each do |attribute| %>
          <% next if attribute.field.nil? %>
          <% next unless attribute.field.visible?(action_name) %>

          <th><%= sortable attribute.name, attribute.name.to_s.titleize %></th>
        <% end %>
        <th></th>
      </tr>
    </thead>

    <tbody>
      <% @records.each do |record| %>
        <tr>
          <% resource.attributes.values.each do |attribute| %>
            <% next if attribute.field.nil? %>
            <% next unless attribute.field.visible?(action_name) %>
            <td><%= render partial: attribute.field.to_partial_path("index"), locals: { field: attribute.field, record: record, resource: resource } %></td>
          <% end %>

          <td>
            <%
            # Generate the correct namespaced paths for actions
            model_name = resource.model.model_name.singular_route_key
            show_path = Rails.application.routes.url_helpers.send("super_admin_madmin_#{model_name}_path", record)
            edit_path = Rails.application.routes.url_helpers.send("edit_super_admin_madmin_#{model_name}_path", record)
            %>
            <%= link_to "View", show_path %>
            <%= link_to "Edit", edit_path %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<div class="pagination">
  <%== pagy_nav @pagy if @pagy.pages > 1 %>
  <span>Showing <%= tag.strong @pagy.in %> of <%= tag.strong @pagy.count %></span>
</div>
