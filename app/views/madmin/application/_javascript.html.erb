<%# Custom madmin asset override for Propshaft compatibility %>
<%# This file overrides the default madmin asset loading to work with Propshaft %>

<%# Basic CSS for madmin interface - inline styles to avoid asset pipeline issues %>
<style>
  /* Basic madmin styles */
  body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
  }
  
  #sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100vh;
    background-color: #1f2937;
    color: white;
    padding: 1rem;
    overflow-y: auto;
  }
  
  #sidebar h1 {
    margin: 0 0 2rem 0;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  #sidebar h1 a {
    color: white;
    text-decoration: none;
  }
  
  #sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  #sidebar nav li {
    margin-bottom: 0.5rem;
  }
  
  #sidebar nav a {
    color: #d1d5db;
    text-decoration: none;
    display: block;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
  }
  
  #sidebar nav a:hover,
  #sidebar nav a.active {
    background-color: #374151;
    color: white;
  }
  
  main {
    margin-left: 250px;
    padding: 2rem;
  }
  
  .header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #1f2937;
  }
  
  .btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-weight: 500;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
  }
  
  .btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
  }
  
  .btn-secondary {
    background-color: #6b7280;
    color: white;
    border-color: #6b7280;
  }
  
  .btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
  }
  
  .btn-danger {
    background-color: #dc2626;
    color: white;
    border-color: #dc2626;
  }
  
  .btn-danger:hover {
    background-color: #b91c1c;
    border-color: #b91c1c;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
  }
  
  tr:hover {
    background-color: #f9fafb;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #374151;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
  }
  
  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
  
  .alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
  }
  
  .alert-danger {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
  }
  
  .alert-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }
  
  .pagination a, .pagination span {
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #374151;
  }
  
  .pagination a:hover {
    background-color: #f3f4f6;
  }
  
  .pagination .current {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
  }
  
  .scopes {
    margin-bottom: 1rem;
  }
  
  .scopes .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .scopes .btn.active {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
  }
</style>

<%# Include Turbo for navigation %>
<%= javascript_importmap_tags %>
