<%# Custom madmin navigation override for namespace compatibility %>
<h1>
  <%= link_to super_admin_madmin_users_path do %>
    <%= Madmin.site_name || "Admin" %>
  <% end %>
</h1>

<nav>
  <ul>
    <%
    # Manually build menu items with correct namespaced paths
    menu_items = [
      { label: "Users", path: super_admin_madmin_users_path },
      { label: "Jobs", path: super_admin_madmin_jobs_path },
      { label: "Chat Requests", path: super_admin_madmin_chat_requests_path },
      { label: "Conversations", path: super_admin_madmin_conversations_path },
      { label: "Messages", path: super_admin_madmin_messages_path },
      { label: "Organizations", path: super_admin_madmin_organizations_path },
      { label: "Talent Profiles", path: super_admin_madmin_talent_profiles_path },
      { label: "Job Applications", path: super_admin_madmin_job_applications_path },
      { label: "Job Invitations", path: super_admin_madmin_job_invitations_path },
      { label: "Roles", path: super_admin_madmin_roles_path },
      { label: "User Roles", path: super_admin_madmin_user_roles_path }
    ]
    %>
    <% menu_items.each do |item| %>
      <li>
        <%= link_to item[:label], item[:path], class: ("active" if current_page?(item[:path])) %>
      </li>
    <% end %>
  </ul>
</nav>
