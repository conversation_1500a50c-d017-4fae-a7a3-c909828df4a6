<% content_for :title, "Jobs" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Jobs</h1>
    <p class="mt-2 text-stone-600">Manage job postings and applications</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search jobs by title or description...",
      filters: [
        { key: 'status', label: 'Status', options: @filter_options[:status] },
        { key: 'job_category', label: 'Category', options: @filter_options[:job_category] },
        { key: 'platform', label: 'Platform', options: @filter_options[:platform] },
        { key: 'budget_range', label: 'Budget Range', options: @filter_options[:budget_range] },
        { key: 'is_premium', label: 'Premium Status', options: @filter_options[:is_premium] }
      ] %>

  <!-- Results Summary -->
  <div class="mb-4">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> jobs
    </p>
  </div>

  <!-- Jobs Table -->
  <%= render 'shared/admin/table', 
      collection: @jobs,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'title', 
          label: 'Title', 
          sortable: true,
          render: ->(job) {
            content_tag(:div) do
              concat content_tag(:div, truncate(job.title, length: 50), class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, job.organization.name, class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'job_category', 
          label: 'Category', 
          sortable: true,
          render: ->(job) {
            content_tag(:span, job.job_category&.humanize || "Not set", 
              class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800")
          }
        },
        { 
          key: 'status', 
          label: 'Status', 
          sortable: true,
          render: ->(job) {
            status_class = case job.status
                          when 'published' then 'bg-green-100 text-green-800'
                          when 'draft' then 'bg-yellow-100 text-yellow-800'
                          when 'expired' then 'bg-red-100 text-red-800'
                          else 'bg-stone-100 text-stone-800'
                          end
            content_tag(:span, job.status.humanize, 
              class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_class}")
          }
        },
        { 
          key: 'budget_range', 
          label: 'Budget', 
          sortable: true,
          render: ->(job) {
            job.budget_range&.humanize&.gsub('_', ' ') || "Not set"
          }
        },
        { 
          key: 'applications', 
          label: 'Applications',
          render: ->(job) {
            count = job.job_applications.count
            content_tag(:div) do
              concat content_tag(:div, count.to_s, class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, "applications", class: "text-xs text-stone-500")
            end
          }
        },
        { 
          key: 'is_premium', 
          label: 'Premium',
          render: ->(job) {
            if job.is_premium?
              content_tag(:span, "Premium", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800")
            else
              content_tag(:span, "Regular", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800")
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true 
        },
        { 
          key: 'application_deadline', 
          label: 'Deadline', 
          sortable: true,
          render: ->(job) {
            if job.application_deadline
              deadline_class = job.application_deadline < Time.current ? 'text-red-600' : 'text-stone-900'
              content_tag(:span, job.application_deadline.strftime('%b %d, %Y'), class: "text-sm #{deadline_class}")
            else
              content_tag(:span, "No deadline", class: "text-sm text-stone-500")
            end
          }
        }
      ],
      actions: true,
      show_path: ->(job) { super_admin_admin_job_path(job) },
      edit_path: ->(job) { edit_super_admin_admin_job_path(job) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
