<% content_for :title, "Admin Dashboard" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Admin Dashboard</h1>
    <p class="mt-2 text-stone-600">Comprehensive overview of system data and activity</p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Users Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Total Users</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:users][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:users][:verified] %> verified, 
              <%= @stats[:users][:scouts] %> scouts, 
              <%= @stats[:users][:talents] %> talents
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Jobs Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Total Jobs</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:jobs][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:jobs][:published] %> published, 
              <%= @stats[:jobs][:draft] %> draft, 
              <%= @stats[:jobs][:expired] %> expired
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Organizations Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Organizations</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:organizations][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:organizations][:with_jobs] %> with jobs
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Communication Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-orange-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Communication</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:communication][:conversations] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:communication][:chat_requests] %> chat requests, 
              <%= @stats[:communication][:messages] %> messages
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white rounded-lg shadow mb-8">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <%= link_to super_admin_admin_users_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Users</h3>
            <p class="text-sm text-stone-500">Manage user accounts</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_jobs_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Jobs</h3>
            <p class="text-sm text-stone-500">Manage job postings</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_chat_requests_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Chat Requests</h3>
            <p class="text-sm text-stone-500">Monitor communications</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_organizations_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Organizations</h3>
            <p class="text-sm text-stone-500">Manage companies</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Users -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Users</h2>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <tbody class="bg-white divide-y divide-stone-200">
            <% @recent_activity[:recent_users].each do |user| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8">
                      <% if user.avatar.attached? %>
                        <%= image_tag user.avatar, class: "h-8 w-8 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-xs font-medium text-stone-700"><%= user.initials %></span>
                        </div>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-stone-900"><%= user.name %></div>
                      <div class="text-sm text-stone-500"><%= user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-stone-500">
                  <%= time_ago_in_words(user.created_at) %> ago
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Recent Jobs -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Jobs</h2>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <tbody class="bg-white divide-y divide-stone-200">
            <% @recent_activity[:recent_jobs].each do |job| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-stone-900"><%= truncate(job.title, length: 40) %></div>
                  <div class="text-sm text-stone-500">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= job.status == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                      <%= job.status.humanize %>
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-stone-500">
                  <%= time_ago_in_words(job.created_at) %> ago
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
