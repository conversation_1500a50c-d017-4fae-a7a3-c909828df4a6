<%# 
  Shared admin pagination component using Pagy
  
  Usage:
  <%= render 'shared/admin/pagination', collection: @pagy %>
%>

<% if collection.respond_to?(:pages) && collection.pages > 1 %>
  <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-stone-200 sm:px-6 rounded-b-lg">
    <div class="flex-1 flex justify-between sm:hidden">
      <!-- Mobile pagination -->
      <% if collection.prev %>
        <%= link_to "Previous", request.params.merge(page: collection.prev), 
            class: "relative inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
      <% else %>
        <span class="relative inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-400 bg-stone-50 cursor-not-allowed">
          Previous
        </span>
      <% end %>
      
      <% if collection.next %>
        <%= link_to "Next", request.params.merge(page: collection.next), 
            class: "ml-3 relative inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
      <% else %>
        <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-400 bg-stone-50 cursor-not-allowed">
          Next
        </span>
      <% end %>
    </div>
    
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-stone-700">
          Showing
          <span class="font-medium"><%= collection.from %></span>
          to
          <span class="font-medium"><%= collection.to %></span>
          of
          <span class="font-medium"><%= collection.count %></span>
          results
        </p>
      </div>
      
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <!-- Previous button -->
          <% if collection.prev %>
            <%= link_to request.params.merge(page: collection.prev), 
                class: "relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
              <span class="sr-only">Previous</span>
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
          <% else %>
            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
              <span class="sr-only">Previous</span>
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </span>
          <% end %>
          
          <!-- Page numbers -->
          <% collection.series.each do |item| %>
            <% if item == :gap %>
              <span class="relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700">
                ...
              </span>
            <% elsif item == collection.page %>
              <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                <%= item %>
              </span>
            <% else %>
              <%= link_to item, request.params.merge(page: item), 
                  class: "relative inline-flex items-center px-4 py-2 border border-stone-300 bg-white text-sm font-medium text-stone-700 hover:bg-stone-50" %>
            <% end %>
          <% end %>
          
          <!-- Next button -->
          <% if collection.next %>
            <%= link_to request.params.merge(page: collection.next), 
                class: "relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-white text-sm font-medium text-stone-500 hover:bg-stone-50" do %>
              <span class="sr-only">Next</span>
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
          <% else %>
            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-stone-300 bg-stone-50 text-sm font-medium text-stone-400 cursor-not-allowed">
              <span class="sr-only">Next</span>
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </span>
          <% end %>
        </nav>
      </div>
    </div>
  </div>
<% end %>
