<%# 
  Shared admin table component
  
  Usage:
  <%= render 'shared/admin/table', 
      collection: @users,
      columns: [
        { key: 'id', label: 'ID', sortable: true },
        { key: 'email', label: 'Email', sortable: true },
        { key: 'created_at', label: 'Created', sortable: true }
      ],
      actions: true,
      show_path: ->(record) { super_admin_admin_user_path(record) },
      edit_path: ->(record) { edit_super_admin_admin_user_path(record) }
  %>
%>

<% 
  columns ||= []
  actions ||= false
  show_path ||= nil
  edit_path ||= nil
  delete_path ||= nil
%>

<div class="bg-white shadow-sm rounded-lg overflow-hidden">
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <% columns.each do |column| %>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
              <% if column[:sortable] && @sort_by %>
                <%= link_to column[:label], 
                    request.params.merge(
                      sort_by: column[:key], 
                      sort_direction: (@sort_by == column[:key].to_s && @sort_direction == 'asc') ? 'desc' : 'asc'
                    ),
                    class: "flex items-center space-x-1 hover:text-stone-700" do %>
                  <span><%= column[:label] %></span>
                  <% if @sort_by == column[:key].to_s %>
                    <% if @sort_direction == 'asc' %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                      </svg>
                    <% else %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    <% end %>
                  <% end %>
                <% end %>
              <% else %>
                <%= column[:label] %>
              <% end %>
            </th>
          <% end %>
          <% if actions %>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
              Actions
            </th>
          <% end %>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% if collection.any? %>
          <% collection.each do |record| %>
            <tr class="hover:bg-stone-50">
              <% columns.each do |column| %>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <% if column[:render] %>
                    <%= column[:render].call(record) %>
                  <% else %>
                    <% value = record.send(column[:key]) %>
                    <% if value.is_a?(Time) || value.is_a?(DateTime) %>
                      <%= value.strftime('%b %d, %Y at %I:%M %p') %>
                    <% elsif value.is_a?(Date) %>
                      <%= value.strftime('%b %d, %Y') %>
                    <% elsif value.is_a?(TrueClass) || value.is_a?(FalseClass) %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= value ? 'Yes' : 'No' %>
                      </span>
                    <% else %>
                      <%= truncate(value.to_s, length: 50) %>
                    <% end %>
                  <% end %>
                </td>
              <% end %>
              <% if actions %>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <% if show_path %>
                    <%= link_to "View", show_path.call(record), 
                        class: "text-stone-600 hover:text-stone-900" %>
                  <% end %>
                  <% if edit_path %>
                    <%= link_to "Edit", edit_path.call(record), 
                        class: "text-blue-600 hover:text-blue-900" %>
                  <% end %>
                  <% if delete_path %>
                    <%= link_to "Delete", delete_path.call(record), 
                        method: :delete,
                        data: { confirm: "Are you sure?" },
                        class: "text-red-600 hover:text-red-900" %>
                  <% end %>
                </td>
              <% end %>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="<%= columns.length + (actions ? 1 : 0) %>" class="px-6 py-12 text-center text-stone-500">
              No records found.
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
