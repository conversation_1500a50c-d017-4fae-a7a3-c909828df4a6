<%# 
  Shared admin search and filters component
  
  Usage:
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search users...",
      filters: [
        { key: 'status', label: 'Status', options: [['Active', 'active'], ['Inactive', 'inactive']] }
      ]
  %>
%>

<% 
  search_placeholder ||= "Search..."
  filters ||= []
%>

<div class="bg-white p-6 rounded-lg shadow-sm mb-6">
  <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
    <div class="flex flex-col lg:flex-row lg:items-end lg:space-x-4 space-y-4 lg:space-y-0">
      <!-- Search Input -->
      <div class="flex-1">
        <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.text_field :search, 
            value: params[:search],
            placeholder: search_placeholder,
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" %>
      </div>
      
      <!-- Filters -->
      <% filters.each do |filter| %>
        <div class="flex-shrink-0">
          <%= form.label filter[:key], filter[:label], class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select filter[:key], 
              options_for_select([['All', '']] + filter[:options], params[filter[:key]]),
              {},
              { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
        </div>
      <% end %>
      
      <!-- Page Size -->
      <div class="flex-shrink-0">
        <%= form.label :page_size, "Per Page", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :page_size, 
            options_for_select([['25', 25], ['50', 50], ['100', 100]], params[:page_size] || 25),
            {},
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
      </div>
      
      <!-- Submit Button -->
      <div class="flex-shrink-0">
        <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
      
      <!-- Clear Button -->
      <% if params[:search].present? || filters.any? { |f| params[f[:key]].present? } %>
        <div class="flex-shrink-0">
          <%= link_to "Clear", request.path, 
              class: "inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        </div>
      <% end %>
    </div>
    
    <!-- Preserve sort parameters -->
    <%= form.hidden_field :sort_by, value: params[:sort_by] if params[:sort_by].present? %>
    <%= form.hidden_field :sort_direction, value: params[:sort_direction] if params[:sort_direction].present? %>
  <% end %>
</div>
