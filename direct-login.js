#!/usr/bin/env node

/**
 * Direct Playwright Login Script
 * 
 * This script uses <PERSON><PERSON> directly to login to your app on localhost:5010
 */

const { chromium } = require('playwright-core');
const readline = require('readline');

async function promptUser(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

async function loginToApp() {
  console.log('🎭 Direct Playwright Login to localhost:5010');
  console.log('=============================================');
  console.log('');

  let browser;
  let page;

  try {
    // Launch browser
    console.log('🚀 Launching browser...');
    browser = await chromium.launch({ 
      headless: false,  // Show the browser so you can see what's happening
      slowMo: 1000      // Slow down actions for visibility
    });
    
    page = await browser.newPage();
    
    // Set viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    
    console.log('✅ Browser launched successfully');
    console.log('');

    // Navigate to your app
    console.log('🌐 Navigating to http://localhost:5010...');
    await page.goto('http://localhost:5010', { waitUntil: 'networkidle' });
    console.log('✅ Successfully navigated to your app');
    console.log('');

    // Take initial screenshot
    console.log('📸 Taking initial screenshot...');
    await page.screenshot({ path: 'app-initial.png', fullPage: true });
    console.log('✅ Screenshot saved as app-initial.png');
    console.log('');

    // Get page title and URL
    const title = await page.title();
    const url = page.url();
    console.log(`📄 Page title: "${title}"`);
    console.log(`🔗 Current URL: ${url}`);
    console.log('');

    // Look for login elements
    console.log('🔍 Analyzing page for login elements...');
    
    // Check for common login selectors
    const loginSelectors = [
      'input[type="email"]',
      'input[name="email"]',
      'input[id*="email"]',
      'input[placeholder*="email" i]',
      'input[type="text"][name*="username"]',
      'input[name="username"]',
      'input[id*="username"]',
      'input[placeholder*="username" i]'
    ];

    const passwordSelectors = [
      'input[type="password"]',
      'input[name="password"]',
      'input[id*="password"]',
      'input[placeholder*="password" i]'
    ];

    const submitSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:has-text("Login")',
      'button:has-text("Sign in")',
      'button:has-text("Log in")',
      '[role="button"]:has-text("Login")',
      '[role="button"]:has-text("Sign in")'
    ];

    // Find email/username field
    let emailField = null;
    for (const selector of loginSelectors) {
      try {
        emailField = await page.locator(selector).first();
        if (await emailField.isVisible()) {
          console.log(`✅ Found email/username field: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    // Find password field
    let passwordField = null;
    for (const selector of passwordSelectors) {
      try {
        passwordField = await page.locator(selector).first();
        if (await passwordField.isVisible()) {
          console.log(`✅ Found password field: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    // Find submit button
    let submitButton = null;
    for (const selector of submitSelectors) {
      try {
        submitButton = await page.locator(selector).first();
        if (await submitButton.isVisible()) {
          console.log(`✅ Found submit button: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    if (emailField && passwordField && submitButton) {
      console.log('');
      console.log('🎉 Found complete login form!');
      console.log('');

      // Get credentials from user
      const email = await promptUser('📧 Enter your email/username: ');
      const password = await promptUser('🔒 Enter your password: ');

      console.log('');
      console.log('🤖 Filling in login form...');

      // Fill in the form
      await emailField.fill(email);
      console.log('✅ Email/username filled');

      await passwordField.fill(password);
      console.log('✅ Password filled');

      // Take screenshot before submitting
      await page.screenshot({ path: 'app-before-submit.png', fullPage: true });
      console.log('📸 Screenshot taken before submit');

      // Submit the form
      console.log('🔘 Clicking submit button...');
      await submitButton.click();

      // Wait for navigation or response
      console.log('⏳ Waiting for login to process...');
      try {
        await page.waitForLoadState('networkidle', { timeout: 10000 });
      } catch (e) {
        console.log('⚠️  Page didn\'t fully load, but continuing...');
      }

      // Take final screenshot
      await page.screenshot({ path: 'app-after-login.png', fullPage: true });
      console.log('📸 Final screenshot taken');

      // Check if login was successful
      const newUrl = page.url();
      const newTitle = await page.title();
      
      console.log('');
      console.log('📊 Login Results:');
      console.log(`📄 New page title: "${newTitle}"`);
      console.log(`🔗 New URL: ${newUrl}`);
      
      if (newUrl !== url) {
        console.log('✅ URL changed - login likely successful!');
      } else {
        console.log('⚠️  URL didn\'t change - check if login was successful');
      }

      console.log('');
      console.log('📸 Screenshots saved:');
      console.log('  • app-initial.png (initial page)');
      console.log('  • app-before-submit.png (form filled)');
      console.log('  • app-after-login.png (after login)');

    } else {
      console.log('');
      console.log('❌ Could not find complete login form');
      console.log('');
      console.log('🔍 What I found:');
      console.log(`  • Email/Username field: ${emailField ? '✅' : '❌'}`);
      console.log(`  • Password field: ${passwordField ? '✅' : '❌'}`);
      console.log(`  • Submit button: ${submitButton ? '✅' : '❌'}`);
      console.log('');
      console.log('💡 The page might:');
      console.log('  • Use different field names or IDs');
      console.log('  • Have a multi-step login process');
      console.log('  • Already be logged in');
      console.log('  • Use a different authentication method');
      
      // Show page content for debugging
      console.log('');
      console.log('📄 Page content (first 500 chars):');
      const content = await page.content();
      console.log(content.substring(0, 500) + '...');
    }

    console.log('');
    console.log('⏸️  Browser will stay open for 30 seconds so you can inspect...');
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Login automation failed:', error.message);
    console.log('');
    console.log('🔧 Make sure your app is running on localhost:5010');
  } finally {
    if (browser) {
      console.log('🔚 Closing browser...');
      await browser.close();
    }
  }
}

// Run the login automation if this script is executed directly
if (require.main === module) {
  loginToApp().catch(console.error);
}

module.exports = { loginToApp };
