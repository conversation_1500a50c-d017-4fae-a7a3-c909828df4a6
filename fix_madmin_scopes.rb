#!/usr/bin/env ruby

# Script to fix madmin resource scope definitions
# Mad<PERSON> expects scope names only, not lambda definitions

files_to_fix = [
  'app/madmin/resources/job_invitation_resource.rb',
  'app/madmin/resources/job_resource.rb',
  'app/madmin/resources/session_resource.rb',
  'app/madmin/resources/organization_membership_resource.rb',
  'app/madmin/resources/job_application_resource.rb',
  'app/madmin/resources/talent_profile_resource.rb',
  'app/madmin/resources/organization_resource.rb',
  'app/madmin/resources/user_role_resource.rb',
  'app/madmin/resources/impersonation_log_resource.rb',
  'app/madmin/resources/message_resource.rb'
]

files_to_fix.each do |file_path|
  next unless File.exist?(file_path)
  
  content = File.read(file_path)
  
  # Replace scope definitions with lambda to just scope names
  updated_content = content.gsub(/scope\s+:(\w+),\s*->\s*\{[^}]+\}/, 'scope :\1')
  
  if content != updated_content
    File.write(file_path, updated_content)
    puts "Fixed #{file_path}"
  else
    puts "No changes needed for #{file_path}"
  end
end

puts "Done fixing madmin resource scope definitions!"
