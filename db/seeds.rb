# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

require 'faker'

# Clear existing data in correct order to respect foreign key constraints
Message.delete_all
ConversationParticipant.delete_all
Conversation.delete_all
JobApplication.delete_all
SavedJob.delete_all # Add this line to delete saved_jobs before jobs
TalentBookmark.delete_all # Add this to delete talent bookmarks
TalentNote.delete_all # Add this to delete talent notes
OrganizationMembership.delete_all
JobInvitation.delete_all # Added: Delete invitations before jobs
Job.delete_all
TalentProfile.delete_all # Added: Delete profiles before users
Session.delete_all # Added: Delete sessions before users
User.delete_all
Organization.delete_all

# Create organizations
organizations = []
5.times do |i|
  org = Organization.create!(name: Faker::Company.name)
  organizations << org
end

puts "Created #{organizations.count} organizations"

# Create scout users (one for each organization)
scouts = []
organizations.each_with_index do |org, i|
  scout =
    User.create!(
      email: "scout#{i + 1}@example.com",
      password: 'password123123',
      verified: true,
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      time_zone: 'UTC',
      onboarding_completed: true, # Scout onboarding is complete
      onboarding_step: 'completed',
      signup_intent: 'scout', # Added
      scout_signup_completed: true, # Added
      talent_signup_completed: false, # Added
      last_logged_in_organization_id: org.id, # Added (Assume they logged into this org)
    )
  scouts << scout

  # Add scout to organization
  OrganizationMembership.create!(
    user: scout,
    organization: org,
    org_role: 'admin',
  )
end

puts "Created #{scouts.count} scouts"

# Create candidate users
20.times do |i|
  # Store onboarding step temporarily
  onboarding_step = %w[personal profile completed].sample
  User.create!(
    email: "candidate#{i + 1}@example.com",
    password: 'password123123',
    verified: [true, false].sample,
    first_name: Faker::Name.first_name,
    last_name: Faker::Name.last_name,
    time_zone: 'UTC',
    onboarding_completed: [true, false].sample, # Keep random for now, might represent scout completion for dual users
    onboarding_step: onboarding_step,
    signup_intent: 'talent', # Added
    scout_signup_completed: false, # Added
    # Set talent_signup_completed based on whether personal details step is done
    talent_signup_completed: %w[profile completed].include?(onboarding_step), # Added
  )
end

puts 'Created 20 candidates'

# Create talent profiles for candidates
candidates = User.where.not(email: scouts.pluck(:email))
# Use the correct availability_status values from the model
availability_statuses = TalentProfile.availability_statuses.keys
location_preferences = TalentProfile.location_preferences.keys
pricing_models = TalentProfile.pricing_models.keys
price_ranges = [[100, 500], [500, 1000], [1000, 2000], [2000, 5000]]

# Define arrays for array fields
ghostwriter_types = [
  'Content Writer',
  'Copywriter',
  'Email Marketer',
  'Social Media Manager',
  'SEO Specialist',
  'Technical Writer',
  'Creative Writer',
]

niches = [
  'Finance',
  'Health & Wellness',
  'Technology',
  'E-commerce',
  'SaaS',
  'Real Estate',
  'Digital Marketing',
  'Self-improvement',
  'Productivity',
  'Artificial Intelligence',
]

social_media_specialties = %w[
  Twitter/X
  LinkedIn
  Instagram
  TikTok
  Facebook
  YouTube
  Pinterest
  Threads
]

achievement_badges = [
  'Top Performer',
  'Client Favorite',
  'Quick Responder',
  'Expert',
  'Verified Professional',
]

outcomes = [
  'Increased Engagement',
  'Lead Generation',
  'Brand Awareness',
  'Sales Conversion',
  'Community Building',
]

candidates.each do |candidate|
  price_min, price_max = price_ranges.sample

  TalentProfile.create!(
    user: candidate,
    bio: Faker::Lorem.paragraph(sentence_count: 3),
    looking_for: Faker::Lorem.paragraph(sentence_count: 2),
    skills:
      [
        'JavaScript',
        'Ruby on Rails',
        'React',
        'Node.js',
        'Python',
        'SQL',
        'HTML',
        'CSS',
        'TypeScript',
        'AWS',
        'Docker',
        'Kubernetes',
        'Git',
        'REST APIs',
        'GraphQL',
      ].sample(5),
    about: Faker::Lorem.paragraph(sentence_count: 4),
    vsl_link: [nil, Faker::Internet.url].sample, # 50% chance of having a VSL link
    availability_status: availability_statuses.sample,
    price_range_min: price_min,
    price_range_max: price_max,
    created_at: Faker::Time.between(from: 30.days.ago, to: Time.current),
    # Add missing fields
    headline: Faker::Job.title,
    location: Faker::Address.city + ', ' + Faker::Address.country,
    location_preference: location_preferences.sample,
    pricing_model: pricing_models.sample,
    is_agency: [true, false].sample,
    portfolio_link: [nil, Faker::Internet.url].sample,
    website_url: [nil, Faker::Internet.url].sample,
    linkedin_url: [
      nil,
      'https://linkedin.com/in/' + Faker::Internet.username,
    ].sample,
    instagram_url: [
      nil,
      'https://instagram.com/' + Faker::Internet.username,
    ].sample,
    x_url: [nil, 'https://x.com/' + Faker::Internet.username].sample,
    threads_url: [
      nil,
      'https://threads.net/' + Faker::Internet.username,
    ].sample,
    platform_choice: %w[Twitter/X LinkedIn Instagram TikTok Facebook].sample,
    ghostwriter_type: ghostwriter_types.sample(rand(1..3)),
    niches: niches.sample(rand(1..4)),
    social_media_specialty: social_media_specialties.sample(rand(1..3)),
    achievement_badges: rand < 0.3 ? achievement_badges.sample(rand(1..2)) : [],
    outcomes: outcomes.sample(rand(1..3)),
  )
end

puts "Created #{TalentProfile.count} talent profiles for candidates"

# Generate jobs for each organization
organizations.each do |org|
  10.times do
    # Map budget to the appropriate enum value
    budget_amount = Faker::Number.between(from: 500, to: 20_000)
    budget_range =
      if budget_amount < 1000
        :under_1000
      elsif budget_amount < 2000
        :range_1000_2000
      elsif budget_amount < 4000
        :range_2000_4000
      elsif budget_amount < 7000
        :range_4000_7000
      else
        :above_7000
      end

    Job.create!(
      title: Faker::Job.title,
      description: Faker::Lorem.paragraph(sentence_count: 5),
      job_category: Job.job_categories.keys.sample,
      budget_range: budget_range,
      notification_preference: Job.notification_preferences.keys.sample,
      outcome: Job.outcomes.keys.sample,
      payment_frequency: Job.payment_frequencies.keys.sample,
      platform: Job.platforms.keys.sample,
      topics:
        [
          'Artificial Intelligence',
          'E-Commerce & Dropshipping',
          'General Business',
          'Health & Wellness',
          'Info Products',
          'Investing & Finance',
          'Masculinity',
          'Marketing & Sales',
          'Productivity',
          'Real Estate',
          'SaaS',
          'Self-Development',
          'SEO',
          'Social Media',
          'Spirituality',
          'Tech & Startups',
          'Web 3',
          'Writing',
        ].sample(rand(1..3)),
      organization_id: org.id,
      # Add missing fields
      application_deadline:
        Faker::Time.between(from: 7.days.from_now, to: 30.days.from_now),
      business_challenge: Faker::Lorem.paragraph(sentence_count: 3),
      charge_per_client: %w[$50-100 $100-200 $200-500 $500+].sample,
      client_count: %w[1-10 10-50 50-100 100+].sample,
      offer_summary: Faker::Lorem.paragraph(sentence_count: 2),
      status: %w[draft published expired].sample,
      useful_info: Faker::Lorem.paragraph(sentence_count: 2),
    )
  end
end

puts "Created #{Job.count} jobs across #{organizations.count} organizations"

# Create job applications with new status stages
users = User.all
jobs = Job.all

# List of all possible statuses
# Replace the existing statuses array with the new ones
statuses = %i[applied reviewed qualified offered accepted]

# Create 3-5 applications for each status
statuses.each do |status|
  rand(3..5).times do
    user = users.sample
    job = jobs.sample

    # Skip if user already applied to this job
    next if JobApplication.exists?(user: user, job: job)

    created_at = Faker::Time.between(from: 30.days.ago, to: Time.current)

    application =
      JobApplication.new(
        user: user,
        job: job,
        application_letter: Faker::Lorem.paragraphs(number: 3).join("\n\n"),
        status: status,
        created_at: created_at,
        updated_at: created_at,
        applied_at: created_at,
      )

    # Attach a dummy resume file
    application.resume.attach(
      io: StringIO.new('Dummy resume content'),
      filename: "resume_#{user.id}.pdf",
      content_type: 'application/pdf',
    )

    # Randomly attach portfolio documents
    if rand < 0.7
      # 70% chance to have portfolio
      2.times do |i|
        application.documents.attach(
          io: StringIO.new("Portfolio document #{i + 1} content"),
          filename: "portfolio_#{user.id}_#{i + 1}.pdf",
          content_type: 'application/pdf',
        )
      end
    end

    application.save!
  end
end

puts 'Created job applications across all status stages'

# Create conversations with a mix of jobs and standalone
20.times { Conversation.create(job: Job.all.sample) }

# Removed line that created standalone conversations

# For each user, create 5 conversations with a mix of archived and active
User.all.each do |user|
  # Create 3 active conversations
  3.times do
    # Get a random user to pair with
    other_user = User.where.not(id: user.id).sample

    # Create or find conversation between these users, ensuring it has a job
    conversation = Conversation.find_or_create_by_participants(user, other_user)

    # Assign a job if the conversation doesn't already have one
    conversation.update!(job: Job.all.sample) unless conversation.job

    # Create messages (3-10 per conversation)
    rand(3..10).times do
      Message.create(
        conversation: conversation,
        user: [user, other_user].sample,
        body:
          "This is a dummy message from #{[user, other_user].sample.first_name}",
        read_at: [nil, Time.now].sample,
      )
    end
  end

  # Create 2 archived conversations
  2.times do
    other_user = User.where.not(id: user.id).sample

    # Ensure conversation has a job if newly created
    conversation = Conversation.find_or_create_by_participants(user, other_user)

    # Assign a job if the conversation doesn't already have one
    conversation.update!(job: Job.all.sample) unless conversation.job

    # Archive this conversation for the user
    participant = conversation.conversation_participants.find_by(user: user)
    participant.archive!

    # Create messages (3-10 per conversation)
    rand(3..10).times do
      Message.create(
        conversation: conversation,
        user: [user, other_user].sample,
        body:
          "This is a dummy message from #{[user, other_user].sample.first_name}",
        read_at: [nil, Time.now].sample,
      )
    end
  end
end

puts "Conversations: #{Conversation.count}"
puts "Standalone Conversations: #{Conversation.where(job: nil).count}"
puts "Conversations with Jobs: #{Conversation.where.not(job: nil).count}"
puts "Messages: #{Message.count}"
puts "Participants: #{ConversationParticipant.count}"
