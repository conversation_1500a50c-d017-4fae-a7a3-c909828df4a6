Directory structure:
└── excid3-madmin/
    ├── README.md
    ├── Appraisals
    ├── CHANGELOG.md
    ├── Gemfile
    ├── madmin.gemspec
    ├── MIT-LICENSE
    ├── Rakefile
    ├── .standard.yml
    ├── app/
    │   ├── assets/
    │   │   ├── config/
    │   │   │   └── madmin_manifest.js
    │   │   └── stylesheets/
    │   │       └── madmin/
    │   │           ├── actiontext.css
    │   │           ├── application-sprockets.css
    │   │           ├── application.css
    │   │           ├── base.css
    │   │           ├── buttons.css
    │   │           ├── forms.css
    │   │           ├── pagination.css
    │   │           ├── reset.css
    │   │           ├── sidebar.css
    │   │           └── tables.css
    │   ├── controllers/
    │   │   └── madmin/
    │   │       ├── application_controller.rb
    │   │       ├── base_controller.rb
    │   │       ├── dashboard_controller.rb
    │   │       └── resource_controller.rb
    │   ├── helpers/
    │   │   └── madmin/
    │   │       ├── application_helper.rb
    │   │       ├── nav_helper.rb
    │   │       └── sort_helper.rb
    │   ├── javascript/
    │   │   └── madmin/
    │   │       ├── application.js
    │   │       └── controllers/
    │   │           ├── application.js
    │   │           ├── index.js
    │   │           ├── nested_form_controller.js
    │   │           └── select_controller.js
    │   └── views/
    │       ├── layouts/
    │       │   └── madmin/
    │       │       └── application.html.erb
    │       └── madmin/
    │           ├── application/
    │           │   ├── _flash.html.erb
    │           │   ├── _form.html.erb
    │           │   ├── _javascript.html.erb
    │           │   ├── _navigation.html.erb
    │           │   ├── edit.html.erb
    │           │   ├── index.html.erb
    │           │   ├── new.html.erb
    │           │   └── show.html.erb
    │           ├── dashboard/
    │           │   └── show.html.erb
    │           ├── fields/
    │           │   ├── attachment/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── attachments/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── belongs_to/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── boolean/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── currency/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── date/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── date_time/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── decimal/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── enum/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── file/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── float/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── has_many/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── has_one/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── integer/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── json/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── nested_has_many/
    │           │   │   ├── _fields.html.erb
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── password/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── polymorphic/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── rich_text/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── select/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── string/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   ├── text/
    │           │   │   ├── _form.html.erb
    │           │   │   ├── _index.html.erb
    │           │   │   └── _show.html.erb
    │           │   └── time/
    │           │       ├── _form.html.erb
    │           │       ├── _index.html.erb
    │           │       └── _show.html.erb
    │           └── shared/
    │               └── _label.html.erb
    ├── config/
    │   └── importmap.rb
    ├── docs/
    │   ├── assets.md
    │   ├── authentication.md
    │   ├── controllers.md
    │   ├── custom_fields.md
    │   ├── resources.md
    │   ├── routes.md
    │   └── views.md
    ├── gemfiles/
    │   ├── rails_7.gemfile
    │   ├── rails_7.gemfile.lock
    │   ├── rails_7_1.gemfile
    │   ├── rails_7_1.gemfile.lock
    │   ├── rails_7_2.gemfile
    │   ├── rails_7_2.gemfile.lock
    │   ├── rails_8_0.gemfile
    │   ├── rails_8_0.gemfile.lock
    │   ├── rails_main.gemfile
    │   ├── rails_main.gemfile.lock
    │   ├── sprockets.gemfile
    │   └── sprockets.gemfile.lock
    ├── lib/
    │   ├── madmin.rb
    │   ├── generators/
    │   │   └── madmin/
    │   │       ├── field/
    │   │       │   ├── field_generator.rb
    │   │       │   └── templates/
    │   │       │       ├── _form.html.erb
    │   │       │       ├── _index.html.erb
    │   │       │       ├── _show.html.erb
    │   │       │       └── field.rb.tt
    │   │       ├── install/
    │   │       │   ├── install_generator.rb
    │   │       │   └── templates/
    │   │       │       ├── controller.rb.tt
    │   │       │       └── routes.rb.tt
    │   │       ├── resource/
    │   │       │   ├── resource_generator.rb
    │   │       │   └── templates/
    │   │       │       ├── controller.rb.tt
    │   │       │       └── resource.rb.tt
    │   │       └── views/
    │   │           ├── edit_generator.rb
    │   │           ├── form_generator.rb
    │   │           ├── index_generator.rb
    │   │           ├── javascript_generator.rb
    │   │           ├── layout_generator.rb
    │   │           ├── navigation_generator.rb
    │   │           ├── new_generator.rb
    │   │           ├── show_generator.rb
    │   │           └── views_generator.rb
    │   ├── madmin/
    │   │   ├── engine.rb
    │   │   ├── field.rb
    │   │   ├── generator_helpers.rb
    │   │   ├── menu.rb
    │   │   ├── namespace.rb
    │   │   ├── resource.rb
    │   │   ├── resource_builder.rb
    │   │   ├── search.rb
    │   │   ├── version.rb
    │   │   ├── view_generator.rb
    │   │   └── fields/
    │   │       ├── attachment.rb
    │   │       ├── attachments.rb
    │   │       ├── belongs_to.rb
    │   │       ├── boolean.rb
    │   │       ├── currency.rb
    │   │       ├── date.rb
    │   │       ├── date_time.rb
    │   │       ├── decimal.rb
    │   │       ├── enum.rb
    │   │       ├── file.rb
    │   │       ├── float.rb
    │   │       ├── has_many.rb
    │   │       ├── has_one.rb
    │   │       ├── integer.rb
    │   │       ├── json.rb
    │   │       ├── nested_has_many.rb
    │   │       ├── password.rb
    │   │       ├── polymorphic.rb
    │   │       ├── rich_text.rb
    │   │       ├── select.rb
    │   │       ├── string.rb
    │   │       ├── text.rb
    │   │       └── time.rb
    │   └── tasks/
    │       └── madmin_tasks.rake
    ├── test/
    │   ├── madmin_test.rb
    │   ├── test_helper.rb
    │   ├── dummy/
    │   │   ├── config.ru
    │   │   ├── Rakefile
    │   │   ├── .browserslistrc
    │   │   ├── app/
    │   │   │   ├── assets/
    │   │   │   │   ├── config/
    │   │   │   │   │   └── manifest.js
    │   │   │   │   ├── images/
    │   │   │   │   │   └── .keep
    │   │   │   │   └── stylesheets/
    │   │   │   │       ├── application.css
    │   │   │   │       └── dummy.css
    │   │   │   ├── channels/
    │   │   │   │   └── application_cable/
    │   │   │   │       ├── channel.rb
    │   │   │   │       └── connection.rb
    │   │   │   ├── controllers/
    │   │   │   │   ├── application_controller.rb
    │   │   │   │   ├── home_controller.rb
    │   │   │   │   ├── concerns/
    │   │   │   │   │   └── .keep
    │   │   │   │   └── madmin/
    │   │   │   │       ├── application_controller.rb
    │   │   │   │       ├── comments_controller.rb
    │   │   │   │       ├── habtms_controller.rb
    │   │   │   │       ├── numericals_controller.rb
    │   │   │   │       ├── posts_controller.rb
    │   │   │   │       ├── teams_controller.rb
    │   │   │   │       ├── users_controller.rb
    │   │   │   │       ├── action_mailbox/
    │   │   │   │       │   └── inbound_emails_controller.rb
    │   │   │   │       ├── action_text/
    │   │   │   │       │   └── rich_texts_controller.rb
    │   │   │   │       ├── active_storage/
    │   │   │   │       │   ├── attachments_controller.rb
    │   │   │   │       │   ├── blobs_controller.rb
    │   │   │   │       │   └── variant_records_controller.rb
    │   │   │   │       ├── paper_trail/
    │   │   │   │       │   └── versions_controller.rb
    │   │   │   │       └── user/
    │   │   │   │           └── connected_accounts_controller.rb
    │   │   │   ├── helpers/
    │   │   │   │   └── application_helper.rb
    │   │   │   ├── javascript/
    │   │   │   │   └── packs/
    │   │   │   │       └── application.js
    │   │   │   ├── jobs/
    │   │   │   │   └── application_job.rb
    │   │   │   ├── madmin/
    │   │   │   │   ├── fields/
    │   │   │   │   │   └── custom_field.rb
    │   │   │   │   └── resources/
    │   │   │   │       ├── comment_resource.rb
    │   │   │   │       ├── habtm_resource.rb
    │   │   │   │       ├── numerical_resource.rb
    │   │   │   │       ├── post_resource.rb
    │   │   │   │       ├── user_resource.rb
    │   │   │   │       ├── action_mailbox/
    │   │   │   │       │   └── inbound_email_resource.rb
    │   │   │   │       ├── action_text/
    │   │   │   │       │   └── rich_text_resource.rb
    │   │   │   │       ├── active_storage/
    │   │   │   │       │   ├── attachment_resource.rb
    │   │   │   │       │   └── blob_resource.rb
    │   │   │   │       └── user/
    │   │   │   │           └── connected_account_resource.rb
    │   │   │   ├── mailboxes/
    │   │   │   │   └── application_mailbox.rb
    │   │   │   ├── mailers/
    │   │   │   │   └── application_mailer.rb
    │   │   │   ├── models/
    │   │   │   │   ├── application_record.rb
    │   │   │   │   ├── comment.rb
    │   │   │   │   ├── habtm.rb
    │   │   │   │   ├── numerical.rb
    │   │   │   │   ├── post.rb
    │   │   │   │   ├── user.rb
    │   │   │   │   ├── concerns/
    │   │   │   │   │   └── .keep
    │   │   │   │   └── user/
    │   │   │   │       └── connected_account.rb
    │   │   │   └── views/
    │   │   │       ├── active_storage/
    │   │   │       │   └── blobs/
    │   │   │       │       └── _blob.html.erb
    │   │   │       ├── home/
    │   │   │       │   └── index.html.erb
    │   │   │       ├── layouts/
    │   │   │       │   ├── application.html.erb
    │   │   │       │   ├── mailer.html.erb
    │   │   │       │   └── mailer.text.erb
    │   │   │       └── madmin/
    │   │   │           └── fields/
    │   │   │               └── custom_field/
    │   │   │                   ├── _form.html.erb
    │   │   │                   ├── _index.html.erb
    │   │   │                   └── _show.html.erb
    │   │   ├── config/
    │   │   │   ├── application.rb
    │   │   │   ├── boot.rb
    │   │   │   ├── cable.yml
    │   │   │   ├── credentials.yml.enc
    │   │   │   ├── database.yml
    │   │   │   ├── environment.rb
    │   │   │   ├── importmap.rb
    │   │   │   ├── master.key
    │   │   │   ├── puma.rb
    │   │   │   ├── routes.rb
    │   │   │   ├── spring.rb
    │   │   │   ├── storage.yml
    │   │   │   ├── environments/
    │   │   │   │   ├── development.rb
    │   │   │   │   ├── production.rb
    │   │   │   │   └── test.rb
    │   │   │   ├── initializers/
    │   │   │   │   ├── application_controller_renderer.rb
    │   │   │   │   ├── assets.rb
    │   │   │   │   ├── backtrace_silencers.rb
    │   │   │   │   ├── content_security_policy.rb
    │   │   │   │   ├── cookies_serializer.rb
    │   │   │   │   ├── dummy.rb
    │   │   │   │   ├── filter_parameter_logging.rb
    │   │   │   │   ├── inflections.rb
    │   │   │   │   ├── mime_types.rb
    │   │   │   │   └── wrap_parameters.rb
    │   │   │   ├── locales/
    │   │   │   │   └── en.yml
    │   │   │   └── routes/
    │   │   │       └── madmin.rb
    │   │   ├── db/
    │   │   │   ├── schema.rb
    │   │   │   ├── seeds.rb
    │   │   │   └── migrate/
    │   │   │       ├── 20200911011935_create_users.rb
    │   │   │       ├── 20200911012152_create_posts.rb
    │   │   │       ├── 20200911012214_create_comments.rb
    │   │   │       ├── 20200911035531_create_active_storage_tables.active_storage.rb
    │   │   │       ├── 20200911035532_create_action_text_tables.action_text.rb
    │   │   │       ├── 20200911145314_create_user_connected_accounts.rb
    │   │   │       ├── 20200911160756_create_action_mailbox_tables.action_mailbox.rb
    │   │   │       ├── 20210117173919_add_habtm_model.rb
    │   │   │       ├── 20210117174119_create_numericals.rb
    │   │   │       ├── 20210614192555_create_versions.rb
    │   │   │       ├── 20210614192556_add_object_changes_to_versions.rb
    │   │   │       ├── 20210915051634_adds_settings_to_user.rb
    │   │   │       └── 20210915141444_add_preferences_to_users.rb
    │   │   ├── lib/
    │   │   │   └── assets/
    │   │   │       └── .keep
    │   │   ├── log/
    │   │   │   └── .keep
    │   │   ├── public/
    │   │   │   ├── 404.html
    │   │   │   ├── 422.html
    │   │   │   └── 500.html
    │   │   └── test/
    │   │       └── models/
    │   │           ├── comment_test.rb
    │   │           ├── numerical_test.rb
    │   │           ├── post_test.rb
    │   │           ├── user_test.rb
    │   │           └── user/
    │   │               └── connected_account_test.rb
    │   ├── fixtures/
    │   │   ├── comments.yml
    │   │   ├── numericals.yml
    │   │   ├── posts.yml
    │   │   ├── users.yml
    │   │   ├── action_text/
    │   │   │   └── rich_texts.yml
    │   │   └── user/
    │   │       └── connected_accounts.yml
    │   ├── integration/
    │   │   └── users_resource_test.rb
    │   └── madmin/
    │       ├── field_test.rb
    │       ├── friendly_id_test.rb
    │       ├── nested_form_test.rb
    │       ├── resource_display_name_test.rb
    │       ├── resource_path_test.rb
    │       ├── resource_test.rb
    │       └── search_test.rb
    └── .github/
        ├── FUNDING.yml
        └── workflows/
            ├── ci.yml
            └── publish_gem.yml


Files Content:

================================================
FILE: README.md
================================================
# Madmin

### 🛠 A robust Admin Interface for Ruby on Rails apps

[![Build Status](https://github.com/excid3/madmin/workflows/Tests/badge.svg)](https://github.com/excid3/madmin/actions) [![Gem Version](https://badge.fury.io/rb/madmin.svg)](https://badge.fury.io/rb/madmin)

Why another Ruby on Rails admin? We wanted an admin that was:

- Familiar and customizable like Rails scaffolds (less DSL)
- Supports all the Rails features out of the box (ActionText, ActionMailbox, has_secure_password, etc)
- Stimulus / Turbolinks / Hotwire ready
- Works with Import maps and Sprockets

![Madmin Screenshot](docs/images/screenshot.png)

## Installation

Add `madmin` to your application's Gemfile:

```bash
bundle add madmin
```

Then run the madmin generator:

```bash
rails g madmin:install
```

This will install Madmin and generate resources for each of the models it finds.

## Resources

Madmin uses `Resource` classes to add models to the admin area.

### Generate a Resource

To generate a resource for a model, you can run:

```bash
rails g madmin:resource ActionText::RichText
```

### Avoid N+1 queries

In case of N+1 queries, you can preload the association by overriding the `scoped_resource` method in the controller:

```ruby
module Madmin
  class PostsController < Madmin::ResourceController
    private

    def scoped_resources
      super.includes(:user)
    end
  end
end

```

## Configuring Views

The views packaged within the gem are a great starting point, but inevitably people will need to be able to customize those views.

You can use the included generator to create the appropriate view files, which can then be customized.

For example, running the following will copy over all of the views into your application that will be used for every resource:

```bash
rails generate madmin:views
```

The view files that are copied over in this case includes all of the standard Rails action views (index, new, edit, show, and \_form), as well as:

- `application.html.erb` (layout file)
- `_javascript.html.erb` (default JavaScript setup)
- `_navigation.html.erb` (renders the navigation/sidebar menu)

As with the other views, you can specifically run the views generator for only the navigation or application layout views:

```bash
rails g madmin:views:navigation
 # -> app/views/madmin/_navigation.html.erb

rails g madmin:views:layout  # Note the layout generator includes the layout, javascript, and navigation files.
 # -> app/views/madmin/application.html.erb
 # -> app/views/madmin/_javascript.html.erb
 # -> app/views/madmin/_navigation.html.erb
```

If you only need to customize specific views, you can restrict which views are copied by the generator:

```bash
rails g madmin:views:index
 # -> app/views/madmin/application/index.html.erb
```

You might want to make some of your model's attributes visible in some views but invisible in others.
The `attribute` method in model_resource.rb gives you that flexibility.

```bash
 # -> app/madmin/resources/book_resource.rb
```

```ruby
class BookResource < Madmin::Resource
  attribute :id, form: false
  attribute :title
  attribute :subtitle, index: false
  attribute :author
  attribute :genre
  attribute :pages, show: false
end
```

You can also scope the copied view(s) to a specific Resource/Model:

```bash
rails generate madmin:views:index Book
 # -> app/views/madmin/books/index.html.erb
```

### Specifying Field Types

You can set a field type as the second argument. Field types may have additional options to render the field UI.


For example, we can use a select for the genre attribute and specify the collection of options to choose from.

```ruby
class BookResource < Madmin::Resource
  attribute :genre, :select, collection: ["Fiction", "Mystery", "Thriller"]
end

## Custom Fields

You can generate a custom field with:

```bash
rails g madmin:field Custom
```

This will create a `CustomField` class in `app/madmin/fields/custom_field.rb`
And the related views:

```bash
# -> app/views/madmin/fields/custom_field/_form.html.erb
# -> app/views/madmin/fields/custom_field/_index.html.erb
# -> app/views/madmin/fields/custom_field/_show.html.erb
```

You can then use this field on our resource:

```ruby
class PostResource < Madmin::Resource
  attribute :title, field: CustomField
end
```

## Authentication

You can use a couple of strategies to authenticate users who are trying to
access your madmin panel: [Authentication Docs](docs/authentication.md)

## Assets
You can customize the JavaScript and CSS assets used by Madmin for your application. To learn how
see the [Assets Doc](docs/assets.md)

## 🙏 Contributing

This project uses Standard for formatting Ruby code. Please make sure to run standardrb before submitting pull requests.

## 📝 License

The gem is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).



================================================
FILE: Appraisals
================================================
appraise "rails-7" do
  gem "rails", "~> 7.0.0"
  gem "sqlite3", "~> 1.4"

  # Ruby 3.4+
  gem "benchmark"
  gem "drb"
  gem "mutex_m"

  # Fixes uninitialized constant ActiveSupport::LoggerThreadSafeLevel::Logger (NameError)
  gem "concurrent-ruby", "< 1.3.5"
end

appraise "rails-7-1" do
  gem "rails", "~> 7.1.0"
  gem "sqlite3", "~> 1.4"
end

appraise "rails-7-2" do
  gem "rails", "~> 7.2.0.beta2"
end

appraise "rails-8-0" do
  gem "rails", "~> 8.0.0"
end

appraise "rails-main" do
  gem "rails", github: "rails/rails"
end

appraise "sprockets" do
  remove_gem "propshaft"
  gem "sprockets-rails"
end



================================================
FILE: CHANGELOG.md
================================================
### Unreleased

### 2.0.4

- Fix sorting with search queries #277

### 2.0.3

- Improve warning when an attribute type can't be inferred

### 2.0.2

- Use `try` so field doesn't raise error when retrieving invalid values
- Don't cast model on find. Let ActiveRecord handle STI.

### 2.0.1

- Add pagination to has_many and nested_has_many fields
- Resource generator now matches the madmin namespace with customizations
  For example: `namespace :madmin, path: :admin do`
- Safely handle missing `config/routes/madmin.rb` for Rails 6.1+
  If this file does not exist, `config/routes.rb` will be used
- Replace flatpickr with date and datetime fields for better accessibility

### 2.0.0

- Remove Tailwind CDN
- Add styles through asset pipeline
- Refactor JavaScript into an Import map (separate from the Rails app)
- Include Rails route helpers in Madmin controllers and views for better integration with the main app
- Add `menu` to resources to allow customizing navigation sort order and add headers for grouping

```ruby
# config/initializers/madmin.rb

# Add a Payments header at the first position in the menu
Madmin.menu.add label: "Payments", position: 0
```

```ruby
class SubscriptionResource < Madmin::Resource
  # Add Subscriptions under the Payments header
  menu parent: "Payments"
end
```

- `member_action` now yields the record to the block

```ruby
class UserResource < Madmin::Resource
  member_action do |user|
    button_to "Impersonate", impersonate_user_path(user)
  end
end
```

### 1.2.10

- Fix compatibility with Pagy 8.x

### 1.2.9

- Fix enum and constant lookup

### 1.2.8

- Relax pagy version dependency - @excid3
- Fix unpermitted params on search - @excid3

### 1.2.7

- Fix importmaps for JS - @excid3

### 1.2.6

- Use stimulus-flatpickr beta 3

### 1.2.5

- Add `Madmin::Fields::File` type for Shrine, Carrierwave, etc - @excid3
- Support isolated namespace models - @excid3
- Use `polymorphic_path` for generating URLs - @excid3
- Automatically link `id` column to show action - @excid3
- Add `Edit` link to index - @excid3
- Fix install generator by adding an ApplicationController to the gem - @excid3

### 1.2.4

- Fix controller inheritance for Rails 6 by making it explicit - @excid3

### 1.2.3

- Upgraded to Stimulus 3.0 - @excid3
- Fix nested forms - @excid3
- Add scrollbar on index for wide tables - @jacobdaddario

### 1.2.2

- Rails 7 support 🚀 - @excid3
- Add support for store_accessors - @jacobdaddario

### 1.2.1

- Handle records not having `created_at` columns when setting the default_sort column - @afomera / @excid3
- Reset page on search submit - @jitingcn
- Catch Pagy overflow errors - @excid3
- Add custom field support - @excid3
- Refactor `Resource.attributes` from an array to hash for faster lookup - @excid3

### 1.2.0

- Allow users to override default sort column and direction on resources
- Add sortable columns on the index for resources
- Don't include `form: false` fields from nested resources in nested resource field params

# 1.1.0

- Add `has_secure_password` support
- Add `madmin:views:javascript` generator
- Fix `madmin:views` generator to copy all templates

# 1.0.2

- Use unpkg for assets instead of skypack. Skypack was missing slimselect css
- Check if Rails UJS is loaded before starting it

# 1.0.1

- Fix belongs_to when nil - @excid3
- Improve support for enums - @excid3

### 1.0.0

- Add view generators - @esmale
- Releasing 1.0.0 to prevent confusion - @excid3

### 1.0.0.beta2

- Ignore autogenerated HABTM models - @excid3

### 1.0.0.beta1

- Use Skypack for CSS & JS - @excid
- Add support for all the Postgres, MySQL, and SQLite types supported by Rails - @excid3
- Add decimal support - @excid3
- Add HABTM example - @excid3
- Added `scope` support to Resources and filtering on index page - @excid3

### 0.1.1

- Open sourced for the first time

### 0.1.0

- Registering the gem



================================================
FILE: Gemfile
================================================
source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

# Declare your gem's dependencies in madmin.gemspec.
# Bundler will treat runtime dependencies like base dependencies, and
# development dependencies will be added by default to the :development group.
gemspec

# Declare any dependencies that are still in development here instead of in
# your gemspec. These might include edge Rails or gems from your path or
# Git. Remember to move these dependencies to your gemspec before releasing
# your gem to rubygems.org.

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development

# Databases to test against
gem "pg"
gem "mysql2"
gem "sqlite3"

gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"



================================================
FILE: madmin.gemspec
================================================
$:.push File.expand_path("lib", __dir__)

# Maintain your gem's version:
require "madmin/version"

# Describe your gem and declare its dependencies:
Gem::Specification.new do |spec|
  spec.name = "madmin"
  spec.version = Madmin::VERSION
  spec.authors = ["Chris Oliver", "Andrea Fomera"]
  spec.email = ["<EMAIL>", "<EMAIL>"]
  spec.homepage = "https://github.com/excid3/madmin"
  spec.summary = "A modern admin for Ruby on Rails apps"
  spec.description = "It's an admin, obviously."
  spec.license = "MIT"

  spec.files = Dir["{app,config,db,lib}/**/*", "MIT-LICENSE", "Rakefile", "README.md"]

  spec.required_ruby_version = ">= 3.2.0"

  spec.add_dependency "rails", ">= 7.0.0"
  spec.add_dependency "pagy", ">= 3.5"
  spec.add_dependency "importmap-rails"
  spec.add_dependency "propshaft"
end



================================================
FILE: MIT-LICENSE
================================================
Copyright 2020 Chris Oliver

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.



================================================
FILE: Rakefile
================================================
begin
  require "bundler/setup"
rescue LoadError
  puts "You must `gem install bundler` and `bundle install` to run rake tasks"
end

require "rdoc/task"

RDoc::Task.new(:rdoc) do |rdoc|
  rdoc.rdoc_dir = "rdoc"
  rdoc.title = "Madmin"
  rdoc.options << "--line-numbers"
  rdoc.rdoc_files.include("README.md")
  rdoc.rdoc_files.include("lib/**/*.rb")
end

require "bundler/gem_tasks"

require "rake/testtask"

APP_RAKEFILE = File.expand_path("test/dummy/Rakefile", __dir__)
load "rails/tasks/engine.rake"
load "rails/tasks/statistics.rake"

Rake::TestTask.new(:test) do |t|
  t.libs << "test"
  t.pattern = "test/**/*_test.rb"
  t.verbose = false
end

task default: :test



================================================
FILE: .standard.yml
================================================
ruby_version: 3.0
ignore:
  - 'test/dummy/**/*'



================================================
FILE: app/assets/config/madmin_manifest.js
================================================
//= link_directory ../stylesheets/madmin .css
//= link_directory ../../javascript/madmin .js
//= link_tree ../../javascript/madmin/controllers .js



================================================
FILE: app/assets/stylesheets/madmin/actiontext.css
================================================
/*
 * Provides a drop-in pointer for the default Trix stylesheet that will format the toolbar and
 * the trix-editor content (whether displayed or under editing). Feel free to incorporate this
 * inclusion directly in any other asset bundle and remove this file.
 *
 *= require trix
*/

/*
 * We need to override trix.css’s image gallery styles to accommodate the
 * <action-text-attachment> element we wrap around attachments. Otherwise,
 * images in galleries will be squished by the max-width: 33%; rule.
*/
.trix-content .attachment-gallery > action-text-attachment,
.trix-content .attachment-gallery > .attachment {
  flex: 1 0 33%;
  padding: 0 0.5em;
  max-width: 33%;
}

.trix-content .attachment-gallery.attachment-gallery--2 > action-text-attachment,
.trix-content .attachment-gallery.attachment-gallery--2 > .attachment, .trix-content .attachment-gallery.attachment-gallery--4 > action-text-attachment,
.trix-content .attachment-gallery.attachment-gallery--4 > .attachment {
  flex-basis: 50%;
  max-width: 50%;
}

.trix-content action-text-attachment .attachment {
  padding: 0 !important;
  max-width: 100% !important;
}



================================================
FILE: app/assets/stylesheets/madmin/application-sprockets.css
================================================
/*
 *= require trix
 *= require madmin/actiontext
 *= require madmin/reset
 *= require madmin/base
 *= require madmin/sidebar
 *= require madmin/buttons
 *= require madmin/forms
 *= require madmin/tables
 *= require madmin/pagination
*/



================================================
FILE: app/assets/stylesheets/madmin/application.css
================================================
@import url("/trix.css");
@import url("/madmin/actiontext.css");
@import url("/madmin/reset.css");
@import url("/madmin/base.css");
@import url("/madmin/sidebar.css");
@import url("/madmin/buttons.css");
@import url("/madmin/forms.css");
@import url("/madmin/tables.css");
@import url("/madmin/pagination.css");




================================================
FILE: app/assets/stylesheets/madmin/base.css
================================================
:root {
  --primary-color: rgb(37 99 235);
  --border-color: rgb(229 231 235);
  --background-color: rgb(249 250 251);
  --text-color: rgb(2 6 23);
  --light-text-color: rgb(71 85 105);
  --sidebar-width: 16rem;
}

body {
  color: var(--text-color);
  font-size: 14px;
}

a {
  color: var(--primary-color);
}

.alert {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 1rem;
  margin-bottom: 1rem;

  ul {
    margin-top: 0.5rem;
    margin-bottom: 0;
    padding-left: 2rem;
  }

  svg {
    display: inline-block;
    height: 1rem;
    margin-right: 0.25rem;
    width: 1rem;
    vertical-align: text-bottom;
  }

  &.alert-danger {
    background-color: oklch(.936 .032 17.717);
    color: oklch(.444 .177 26.899);

    svg {
      color: oklch(.637 .237 25.331);
    }
  }

  &.alert-notice {
    background-color: oklch(.962 .044 156.743);
    color: oklch(.448 .119 151.328);

    svg {
      color: oklch(.723 .219 149.579);
    }
  }
}

.search {
  display: flex;
  align-items: center;
}

.header {
  border-bottom: 1px solid rgb(229 231 235);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  margin-bottom: 1rem;

  h1 {
    margin: 0;
  }

  a {
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .actions {
    align-items: center;
    display: flex;
    gap: 0.5rem;
  }
}

.metrics {
  display: flex;

  .metric {
    border: 1px solid rgb(229 231 235);
    border-radius: 0.25rem;
    padding: 1rem;
    margin: 1rem;
    flex: 1 1 0%;

    h4 {
      color: rgb(75 85 99);
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 2rem;
      font-weight: 600;
      margin: 0;
    }
  }
}

.scopes {
  margin-bottom: 1rem;
}



================================================
FILE: app/assets/stylesheets/madmin/buttons.css
================================================
.btn {
  border-radius: 0.375rem;
  display: inline-block;
  font-weight: 600;
  text-decoration: none;

  font-size: 0.875rem;
  line-height: 1.25rem;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;

  &:hover {
    text-decoration: none !important;
  }

  &.btn-primary {
    background-color: var(--primary-color);
    color: white;
  }

  &.btn-secondary {
    background-color: white;
    color: rgb(31 41 55);
    box-shadow: inset 0 0 0 1px rgb(156 163 175);

    &:hover {
      background-color: rgb(243 244 246);
    }

    &.active {
      background-color: rgb(243 244 246);
    }
  }

  &.btn-danger {
    background-color: white;
    color: rgb(239 68 68);
    box-shadow: inset 0 0 0 1px rgb(239 68 68);

    &:hover {
      background-color: rgb(243 244 246);
    }
  }
}



================================================
FILE: app/assets/stylesheets/madmin/forms.css
================================================
.form-hint {
  font-size: 0.875rem;
  margin-top: 0.5rem;

  &.error {
    color: rgb(239 68 68);
  }
}

.form-group {
  margin-bottom: 1rem;
}

.form-input {
  width: 100%;
}

.form-description {
  color: var(--light-text-color);
}

label {
  color: rgb(17 24 39);
  line-height: 1.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

[type='text'], input:where(:not([type])), [type='email'], [type='url'], [type='password'], [type='number'], [type='date'], [type='datetime-local'], [type='month'], [type='search'], [type='tel'], [type='time'], [type='week'], [multiple], textarea, select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #fff;

  border-radius: 0.375rem;
  box-shadow: inset 0 0 0 1px rgb(156 163 175);
  line-height: 1.5rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;

  &:focus {
    box-shadow: inset 0 0 0 1px rgb(var(--primary-color));
  }
}

.required {
  color: rgb(239 68 68);
  font-weight: 600;
}



================================================
FILE: app/assets/stylesheets/madmin/pagination.css
================================================
.pagination {
  padding: 0.5rem;
}

.pagy {
  display: inline-flex;
  isolation: isolate;
  border-radius: 0.375rem;

  a {
    display: inline-flex;
    position: relative;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    align-items: center;
    box-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    --tw-ring-inset: inset;
    --tw-ring-color: #D1D5DB;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #111827;
    background-color: #ffffff;
    border-radius: 0.25rem;
    text-decoration: none;
  }

  a:hover {
    background-color: #F3F4F6;
  }

  a:not([href]) {
    color: #D1D5DB;
    cursor: default;
  }

  a.current {
    color: #ffffff;
    background-color: var(--primary-color);
  }

  label {
    display: inline-block;
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0.5rem;
    white-space: nowrap;
    background-color: #E5E7EB;
  }

  label input {
    border-radius: 0.375rem;
    border-style: none;
    background-color: #F3F4F6;
  }
}



================================================
FILE: app/assets/stylesheets/madmin/reset.css
================================================
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}
input, button, textarea, select {
  font: inherit;
}
p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}
p {
  text-wrap: pretty;
}
h1, h2, h3, h4, h5, h6 {
  text-wrap: balance;
}

/*! modern-normalize v3.0.1 | MIT License | https://github.com/sindresorhus/modern-normalize */

/*
Document
========
*/

/**
Use a better box model (opinionated).
*/

*,
::before,
::after {
	box-sizing: border-box;
}

html {
	/* Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3) */
	font-family:
		system-ui,
		'Segoe UI',
		Roboto,
		Helvetica,
		Arial,
		sans-serif,
		'Apple Color Emoji',
		'Segoe UI Emoji';
	line-height: 1.15; /* 1. Correct the line height in all browsers. */
	-webkit-text-size-adjust: 100%; /* 2. Prevent adjustments of font size after orientation changes in iOS. */
	tab-size: 4; /* 3. Use a more readable tab size (opinionated). */
}

/*
Sections
========
*/

body {
	margin: 0; /* Remove the margin in all browsers. */
}

/*
Text-level semantics
====================
*/

/**
Add the correct font weight in Chrome and Safari.
*/

b,
strong {
	font-weight: bolder;
}

/**
1. Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
2. Correct the odd 'em' font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Consolas,
		'Liberation Mono',
		Menlo,
		monospace; /* 1 */
	font-size: 1em; /* 2 */
}

/**
Add the correct font size in all browsers.
*/

small {
	font-size: 80%;
}

/**
Prevent 'sub' and 'sup' elements from affecting the line height in all browsers.
*/

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/*
Tabular data
============
*/

/**
Correct table border color inheritance in Chrome and Safari. (https://issues.chromium.org/issues/40615503, https://bugs.webkit.org/show_bug.cgi?id=195016)
*/

table {
	border-color: currentcolor;
}

/*
Forms
=====
*/

/**
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
*/

button,
input,
optgroup,
select,
textarea {
	font-family: inherit; /* 1 */
	font-size: 100%; /* 1 */
	line-height: 1.15; /* 1 */
	margin: 0; /* 2 */
}

/**
Correct the inability to style clickable types in iOS and Safari.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
	-webkit-appearance: button;
}

/**
Remove the padding so developers are not caught out when they zero out 'fieldset' elements in all browsers.
*/

legend {
	padding: 0;
}

/**
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
	vertical-align: baseline;
}

/**
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}

/**
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
	-webkit-appearance: textfield; /* 1 */
	outline-offset: -2px; /* 2 */
}

/**
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to 'inherit' in Safari.
*/

::-webkit-file-upload-button {
	-webkit-appearance: button; /* 1 */
	font: inherit; /* 2 */
}

/*
Interactive
===========
*/

/*
Add the correct display in Chrome and Safari.
*/

summary {
	display: list-item;
}



================================================
FILE: app/assets/stylesheets/madmin/sidebar.css
================================================
main {
  padding-top: 1rem;
  padding-right: 1rem;
  padding-bottom: 1rem;
  padding-left: calc(1rem + var(--sidebar-width));
}

#sidebar {
  border-right: 1px solid var(--border-color);
  height: 100%;
  margin: 0;
  overflow: auto;
  padding: 1rem;
  position: fixed;
  width: var(--sidebar-width);

  h1 {
    margin-top: 0;

    a {
      color: var(--text-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  nav {
    h4 {
      margin-top: 0.5rem;
      margin-bottom: 0.25rem;
    }

    a {
      border-radius: .375rem;
      color: var(--text-color);
      display: block;
      font-weight: 500;
      padding: 0.5rem;
      text-decoration: none;

      margin-top: 0.1rem;
      margin-bottom: 0.1rem;

      &:hover {
        background-color: rgb(243 244 246);
      }

      &.active {
        background-color: rgb(243 244 246);
        font-weight: 600;
      }
    }
  }

  footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-top: 1rem;

    a {
      color: rgb(75 85 99);
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem;
      text-decoration: none;

      svg {
        display: inline-block;
      }
    }

    &:hover {
      color: var(--primary-color);
    }
  }
}



================================================
FILE: app/assets/stylesheets/madmin/tables.css
================================================
.table-scroll {
  border-radius: 6px;
  border: 1px solid var(--border-color);
  overflow-x: auto;
  position: relative;
}

table {
  border-collapse: collapse;
  min-width: 100%;

  th {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    text-align: left;
    padding-bottom: 0.75rem;
    padding-top: 0.75rem;
    padding-right: 0.875rem;
    padding-left: 0.875rem;

    a {
      color: rgb(17 24 39);
      text-decoration: none;
    }

    svg {
      display: inline-block;
      vertical-align: middle;
    }

    &.label {
      background-color: var(--background-color);
      border-right: 1px solid var(--border-color);
      width: 16rem;
    }
  }

  td {
    padding-bottom: 0.75rem;
    padding-top: 0.75rem;
    padding-right: 0.875rem;
    padding-left: 0.875rem;

    a {
      font-weight: 500;
    }
  }

  tr {
    border-bottom: 1px solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }
  }
}



================================================
FILE: app/controllers/madmin/application_controller.rb
================================================
module Madmin
  class ApplicationController < Madmin::BaseController
    include Rails.application.routes.url_helpers

    before_action :authenticate_admin_user

    def authenticate_admin_user
      # TODO: Add your authentication logic here

      # For example, with Rails 8 authentication
      # redirect_to "/", alert: "Not authorized." unless authenticated? && Current.user.admin?

      # Or with Devise
      # redirect_to "/", alert: "Not authorized." unless current_user&.admin?
    end
  end
end



================================================
FILE: app/controllers/madmin/base_controller.rb
================================================
module Madmin
  class BaseController < ActionController::Base
    include ::ActiveStorage::SetCurrent if defined?(::ActiveStorage)
    include Pagy::Backend

    protect_from_forgery with: :exception
  end
end



================================================
FILE: app/controllers/madmin/dashboard_controller.rb
================================================
module Madmin
  class DashboardController < Madmin::ApplicationController
    def show
    end
  end
end



================================================
FILE: app/controllers/madmin/resource_controller.rb
================================================
module Madmin
  class ResourceController < Madmin::ApplicationController
    include SortHelper

    before_action :set_record, except: [:index, :new, :create]

    # Assign current_user for paper_trail gem
    before_action :set_paper_trail_whodunnit, if: -> { respond_to?(:set_paper_trail_whodunnit, true) }

    def index
      @pagy, @records = pagy(scoped_resources)

      respond_to do |format|
        format.html
        format.json {
          render json: @records.map { |r| {name: @resource.display_name(r), id: r.id} }
        }
      end
    rescue Pagy::OverflowError
      params[:page] = 1
      retry
    end

    def show
    end

    def new
      @record = resource.model.new(new_resource_params)
    end

    def create
      @record = resource.model.new(resource_params)
      if @record.save
        redirect_to resource.show_path(@record)
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @record.update(resource_params)
        redirect_to resource.show_path(@record)
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @record.destroy
      redirect_to resource.index_path
    end

    private

    def set_record
      @record = resource.model_find(params[:id])
    end

    def resource
      @resource ||= resource_name.constantize
    end
    helper_method :resource

    def resource_name
      "#{controller_path.singularize}_resource".delete_prefix("madmin/").classify
    end

    def scoped_resources
      resources = resource.model.send(valid_scope)
      resources = Madmin::Search.new(resources, resource, search_term).run
      resources.reorder(sort_column => sort_direction)
    end

    def valid_scope
      scope = params.fetch(:scope, "all")
      resource.scopes.include?(scope.to_sym) ? scope : :all
    end

    def resource_params
      params.require(resource.param_key)
        .permit(*resource.permitted_params)
        .transform_values { |v| change_polymorphic(v) }
    end

    def new_resource_params
      params.fetch(resource.param_key, {}).permit!
        .permit(*resource.permitted_params)
        .transform_values { |v| change_polymorphic(v) }
    end

    def change_polymorphic(data)
      return data unless data.is_a?(ActionController::Parameters) && data[:type]

      if data[:type] == "polymorphic"
        GlobalID::Locator.locate(data[:value])
      else
        raise "Unrecognised param data: #{data.inspect}"
      end
    end

    def search_term
      @search_term ||= params[:q].to_s.strip
    end
  end
end



================================================
FILE: app/helpers/madmin/application_helper.rb
================================================
module Madmin
  module ApplicationHelper
    include Pagy::Frontend
    include Rails.application.routes.url_helpers

    def clear_search_params
      resource.index_path(sort: params[:sort], direction: params[:direction])
    end
  end
end



================================================
FILE: app/helpers/madmin/nav_helper.rb
================================================
module Madmin::NavHelper
  def nav_link_to(name = nil, options = {}, html_options = {}, &block)
    if block
      html_options = options
      options = name
      name = block
    end

    url = url_for(options)
    starts_with = html_options.delete(:starts_with)
    html_options[:class] = Array.wrap(html_options[:class])
    active_class = html_options.delete(:active_class) || "active"
    inactive_class = html_options.delete(:inactive_class) || ""

    active = if (paths = Array.wrap(starts_with)) && paths.present?
      paths.any? { |path| request.path.start_with?(path) }
    else
      request.path == url
    end

    classes = active ? active_class : inactive_class
    html_options[:class] << classes unless classes.empty?

    html_options.except!(:class) if html_options[:class].empty?

    return link_to url, html_options, &block if block

    link_to name, url, html_options
  end
end



================================================
FILE: app/helpers/madmin/sort_helper.rb
================================================
module Madmin
  module SortHelper
    def sortable(column, title, options = {})
      matching_column = (column.to_s == sort_column)
      direction = (sort_direction == "asc") ? "desc" : "asc"

      link_to resource.index_path(sort: column, direction: direction, scope: params[:scope], q: params[:q]), options do
        concat title
        if matching_column
          concat " "
          concat tag.span((sort_direction == "asc") ? asc_icon : desc_icon)
        end
      end
    end

    def sort_column
      resource.sortable_columns.include?(params[:sort]) ? params[:sort] : default_sort_column
    end

    def sort_direction
      ["asc", "desc"].include?(params[:direction]) ? params[:direction] : default_sort_direction
    end

    def default_sort_column
      resource.try(:default_sort_column) || (["created_at", "id", "uuid"] & resource.model.column_names).first
    end

    def default_sort_direction
      resource.try(:default_sort_direction) || "desc"
    end

    def asc_icon
      <<~SVG.html_safe
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" height="1rem" width="1rem">
          <path fill-rule="evenodd" d="M11.78 9.78a.75.75 0 0 1-1.06 0L8 7.06 5.28 9.78a.75.75 0 0 1-1.06-1.06l3.25-3.25a.75.75 0 0 1 1.06 0l3.25 3.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" />
        </svg>
      SVG
    end

    def desc_icon
      <<~SVG.html_safe
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" height="1rem" width="1rem">
          <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
        </svg>
      SVG
    end
  end
end



================================================
FILE: app/javascript/madmin/application.js
================================================
import "@hotwired/turbo-rails"
import "trix"
import "@rails/actiontext"
import "controllers"



================================================
FILE: app/javascript/madmin/controllers/application.js
================================================
import { Application } from "@hotwired/stimulus"

const application = Application.start()

// Configure Stimulus development experience
application.debug = false
window.Stimulus   = application

export { application }

import { Dropdown } from "tailwindcss-stimulus-components"
application.register("dropdown", Dropdown)



================================================
FILE: app/javascript/madmin/controllers/index.js
================================================
import { application } from "controllers/application"

// Eager load all controllers defined in the import map under controllers/**/*_controller
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"
eagerLoadControllersFrom("controllers", application)



================================================
FILE: app/javascript/madmin/controllers/nested_form_controller.js
================================================
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static get targets() {
    return [ "links", "template" ]
  }

  connect() {
    this.wrapperClass = this.data.get("wrapperClass") || "nested-fields"
  }

  add_association(event) {
    event.preventDefault()

    var content = this.templateTarget.innerHTML.replace(/NEW_RECORD/g, new Date().getTime())
    this.linksTarget.insertAdjacentHTML('beforebegin', content)
  }

  remove_association(event) {
    event.preventDefault()

    let wrapper = event.target.closest("." + this.wrapperClass)

    // New records are simply removed from the page
    if (wrapper.dataset.newRecord == "true") {
      wrapper.remove()

      // Existing records are hidden and flagged for deletion
    } else {
      wrapper.querySelector("input[name*='_destroy']").value = 1
      wrapper.style.display = 'none'
    }
  }
}



================================================
FILE: app/javascript/madmin/controllers/select_controller.js
================================================
import { Controller } from "@hotwired/stimulus"
import TomSelect from "tom-select"

export default class extends Controller {
  static values = {
    options: Object,
    url: String
  }

  connect() {
    this.select = new TomSelect(this.element, {
      plugins: ['remove_button'],
      valueField: 'id',
      labelField: 'name',
      searchField: 'name',
      load: (search, callback) => {
        let url = search ? `${this.urlValue}?q=${search}` : this.urlValue;
        fetch(url)
          .then(response => response.json())
          .then(json => {
            callback(json);
          }).catch(() => {
            callback();
          });
      }
    })
  }

  disconnect() {
    this.select.destroy()
  }
}



================================================
FILE: app/views/layouts/madmin/application.html.erb
================================================
<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <meta charset="utf-8">
    <meta name="ROBOTS" content="NOODP">
    <meta name="viewport" content="initial-scale=1">
    <title>
      <% if content_for? :title %>
        <%= yield(:title) %> -
      <% end %>
      <%= Madmin.site_name %> Admin
    </title>
    <%= csrf_meta_tags %>
    <%= render "javascript" %>
  </head>
  <body>
    <aside id="sidebar">
      <%= render "navigation" %>
    </aside>
    <main>
      <%= render "flash" %>
      <%= yield %>
    </main>
  </body>
</html>



================================================
FILE: app/views/madmin/application/_flash.html.erb
================================================
<% if alert %>
  <div class="alert alert-danger">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor"><path fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM8 4a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3A.75.75 0 0 1 8 4Zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd"></path></svg>
    <%= alert %>
  </div>
<% end %>

<% if notice %>
  <div class="alert alert-notice">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor"><path fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14Zm3.844-8.791a.75.75 0 0 0-1.188-.918l-3.7 4.79-1.649-1.833a.75.75 0 1 0-1.114 1.004l2.25 2.5a.75.75 0 0 0 1.15-.043l4.25-5.5Z" clip-rule="evenodd"></path></svg>
    <%= notice %>
  </div>
<% end %>



================================================
FILE: app/views/madmin/application/_form.html.erb
================================================
<%= form_with model: [:madmin, record], url: (record.persisted? ? resource.show_path(record) : resource.index_path), local: true do |form| %>
  <% if form.object.errors.any? %>
    <div class="alert alert-danger">
      <div class="">There was <%= pluralize form.object.errors.full_messages.count, "error" %> with your submission:</div>

      <ul>
        <% form.object.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <% resource.attributes.values.select{ _1.field.present? && _1.field.visible?(action_name) }.each do |attribute| %>
    <div class="form-group">
      <%= render "madmin/shared/label", form: form, field: attribute.field %>
      <%= render partial: attribute.field.to_partial_path("form"), locals: { field: attribute.field, record: record, form: form, resource: resource } %>
      <%= tag.div attribute.field.options.description, class: "form-description" if attribute.field.options.description.present? %>
    </div>
  <% end %>

  <%= form.submit class: "btn btn-primary" %>
  <%= link_to "Cancel", (record.persisted? ? resource.show_path(record) : resource.index_path), class: "btn" %>
<% end %>



================================================
FILE: app/views/madmin/application/_javascript.html.erb
================================================
<%= javascript_importmap_tags "application", importmap: Madmin.importmap %>

<%= stylesheet_link_tag *Madmin.stylesheets, "data-turbo-track": "reload" %>
<%= stylesheet_link_tag "https://unpkg.com/flatpickr/dist/flatpickr.min.css", "data-turbo-track": "reload" %>
<%= stylesheet_link_tag "https://unpkg.com/tom-select/dist/css/tom-select.min.css", "data-turbo-track": "reload" %>



================================================
FILE: app/views/madmin/application/_navigation.html.erb
================================================
<h1><%= link_to_if respond_to?(:root_url), Madmin.site_name, root_url, data: {turbo: false}  %></h1>

<nav>
  <%= nav_link_to "Dashboard", madmin_root_path %>

  <% Madmin.menu.render do |item| %>
    <% if item.url %>
      <%= nav_link_to item.label, item.url, starts_with: item.url %>
    <% else %>
      <h4><%= item.label %></h4>
    <% end %>

    <% item.items.each do |item| %>
      <%= nav_link_to item.label, item.url, starts_with: item.url %>
    <% end %>
  <% end %>
</nav>

<footer>
  <%= link_to "https://github.com/excid3/madmin", target: :_blank do %>
    <svg viewBox="0 0 16 16" height="1rem" width="1rem" fill="currentColor" aria-hidden="true"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path></svg>
    Madmin on GitHub
  <% end %>
</footer>



================================================
FILE: app/views/madmin/application/edit.html.erb
================================================
<%= content_for :title, "New #{resource.display_name(@record)}" %>

<header class="header">
  <h1>
    <%= link_to resource.friendly_name.pluralize, resource.index_path %>
    /
    <strong>Edit <%= link_to resource.display_name(@record), resource.show_path(@record) %></strong>
  </h1>
</header>

<%= render partial: "form", locals: { record: @record, resource: resource } %>



================================================
FILE: app/views/madmin/application/index.html.erb
================================================
<%= content_for :title, resource.friendly_name.pluralize %>

<header class="header">
  <h1><%= resource.friendly_name.pluralize %></h1>

  <div class="actions">
    <form class="search">
      <%= hidden_field_tag :page, params[:page], value: 1 %>
      <%= search_field_tag :q, params[:q], placeholder: "Search" %>
    </form>

    <%= link_to clear_search_params, class: "btn btn-secondary" do %>
      <svg xmlns="http://www.w3.org/2000/svg" height="1rem" width="1rem" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
      <% end if params[:q].present? %>

    <%= link_to "New #{resource.friendly_name}", resource.new_path, class: "btn btn-secondary" %>
  </div>
</header>

<nav class="scopes">
  <% if resource.scopes.any? %>
    <%= link_to "All", resource.index_path, class: class_names("btn btn-secondary", {"active" => params[:scope].blank?}) %>
  <% end %>

  <% resource.scopes.each do |scope| %>
    <%= link_to scope.to_s.humanize, resource.index_path(scope: scope, q: params[:q], sort: params[:sort], direction: params[:direction]), class: class_names("btn btn-secondary", {"active" => params[:scope] == scope.to_s}) %>
  <% end %>
</nav>

<div class="table-scroll">
  <table>
    <thead>
      <tr>
        <% resource.attributes.values.each do |attribute| %>
          <% next if attribute.field.nil? %>
          <% next unless attribute.field.visible?(action_name) %>

          <th><%= sortable attribute.name, attribute.name.to_s.titleize %></th>
        <% end %>
        <th></th>
      </tr>
    </thead>

    <tbody>
      <% @records.each do |record| %>
        <tr>
          <% resource.attributes.values.each do |attribute| %>
            <% next if attribute.field.nil? %>
            <% next unless attribute.field.visible?(action_name) %>
            <td><%= render partial: attribute.field.to_partial_path("index"), locals: { field: attribute.field, record: record, resource: resource } %></td>
          <% end %>

          <td>
            <%= link_to "View", resource.show_path(record) %>
            <%= link_to "Edit", resource.edit_path(record) %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<div class="pagination">
  <%== pagy_nav @pagy if @pagy.pages > 1 %>
  <span>Showing <%= tag.strong @pagy.in %> of <%= tag.strong @pagy.count %></span>
</div>



================================================
FILE: app/views/madmin/application/new.html.erb
================================================
<%= content_for :title, "New #{resource.friendly_name}" %>

<header class="header">
  <h1>
    <%= link_to resource.friendly_name.pluralize, resource.index_path %>
    /
    <strong>New <%= resource.friendly_name %></strong>
  </h1>
</header>

<%= render partial: "form", locals: { record: @record, resource: resource } %>



================================================
FILE: app/views/madmin/application/show.html.erb
================================================
<%= content_for :title, resource.display_name(@record) %>

<header class="header">
  <h1>
    <%= link_to resource.friendly_name.pluralize, resource.index_path %>
    /
    <%= resource.display_name(@record) %>
  </h1>

  <div class="actions">
    <% resource.member_actions.each do |action| %>
      <%= instance_exec(@record, &action) %>
    <% end %>
    <%= link_to "Edit", resource.edit_path(@record), class: "btn btn-secondary" %>
    <%= button_to "Delete", resource.show_path(@record), method: :delete, data: { turbo_confirm: "Are you sure?" }, class: "btn btn-danger" %>
  </div>
</header>

<div class="table-scroll">
  <table>
    <tbody>
    <% resource.attributes.values.each do |attribute| %>
      <% next if attribute.field.nil? %>
      <% next unless attribute.field.visible?(action_name) %>

      <tr>
        <th class="label">
          <%= attribute.field.options.label || attribute.name.to_s.titleize %>
        </th>

        <td>
          <%= render partial: attribute.field.to_partial_path("show"), locals: { field: attribute.field, record: @record, resource: resource } %>
        </td>
      </tr>
    <% end %>
    </tbody>
  </table>
</div>



================================================
FILE: app/views/madmin/dashboard/show.html.erb
================================================
<div class="prose">
  <h1>Madmin Dashboard</h1>
  <p>Create <code>app/views/madmin/dashboard/show.html.erb</code> to customize your dashboard.</p>
</div>



================================================
FILE: app/views/madmin/fields/attachment/_form.html.erb
================================================
<%= form.file_field field.to_param, class: "form-input"%>

<% if form.object.persisted? %>
  <div class="mt-2">
    <%= render partial: field.to_partial_path("show"), locals: {field: field, record: record} %>

    <% if (value = field.value(record) && value&.attached?) %>
      <%= link_to "Remove", Madmin.resource_for(value).show_path(value), data: { turbo_method: :delete, turbo_confirm: "Are you sure? You will lose any other changes."} %>
    <% end %>
  </div>
<% end %>



================================================
FILE: app/views/madmin/fields/attachment/_index.html.erb
================================================
<% if (attachment = field.value(record)) && attachment.attached? %>
  <% if attachment.variable? %>
    <%= image_tag main_app.url_for(attachment), class: "max-h-8" %>
  <% else %>
    <%= attachment.filename %>
  <% end %>
<% end %>



================================================
FILE: app/views/madmin/fields/attachment/_show.html.erb
================================================
<% if (attachment = field.value(record)) && attachment.attached? %>
  <% if attachment.variable? %>
    <%= image_tag main_app.url_for(attachment), class: "max-h-32" %>
  <% else %>
    <%= link_to attachment.filename, main_app.url_for(attachment), target: :_blank, class: "text-blue-500 underline" %>
  <% end %>

  <%= link_to "Remove", Madmin.resource_for(attachment).show_path(attachment), data: { turbo_method: :delete, turbo_confirm: "Are you sure? You will lose any other changes."} %>
<% end %>



================================================
FILE: app/views/madmin/fields/attachments/_form.html.erb
================================================
<%= form.file_field field.attribute_name, multiple: true, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/attachments/_index.html.erb
================================================
<%= pluralize field.value(record).count, "attachment" %>



================================================
FILE: app/views/madmin/fields/attachments/_show.html.erb
================================================
<% if (attachments = field.value(record)) && attachments.attached? %>
  <% attachments.each do |attachment| %>
    <% if attachment.variable? %>
      <%= link_to main_app.url_for(attachment), target: :_blank do %>
        <%= image_tag main_app.url_for(attachment), class: "max-h-32" %>
      <% end %>
    <% else %>
      <%= link_to attachment.filename, main_app.url_for(attachment), target: :_blank, class: "text-blue-500 underline" %>
    <% end %>
  <% end %>
<% end %>



================================================
FILE: app/views/madmin/fields/belongs_to/_form.html.erb
================================================
<%= form.select field.to_param, field.options_for_select(record), { prompt: true }, { data: { controller: "select", select_url_value: field.index_path } } %>



================================================
FILE: app/views/madmin/fields/belongs_to/_index.html.erb
================================================
<% if (object = field.value(record)) %>
  <% object_resource = Madmin.resource_for(object) %>
  <%= link_to object_resource.display_name(object), object_resource.show_path(object) %>
<% end %>



================================================
FILE: app/views/madmin/fields/belongs_to/_show.html.erb
================================================
<% if (object = field.value(record)) %>
  <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object), class: "text-blue-500 underline" %>
<% end %>



================================================
FILE: app/views/madmin/fields/boolean/_form.html.erb
================================================
<div class="form-input">
  <%= form.check_box field.attribute_name %>
 </div>



================================================
FILE: app/views/madmin/fields/boolean/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/boolean/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/currency/_form.html.erb
================================================
<%= form.number_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/currency/_index.html.erb
================================================
<%= number_to_currency field.value(record) if field.value(record) %>



================================================
FILE: app/views/madmin/fields/currency/_show.html.erb
================================================
<%= number_to_currency field.value(record) if field.value(record) %>



================================================
FILE: app/views/madmin/fields/date/_form.html.erb
================================================
<%= form.date_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/date/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/date/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/date_time/_form.html.erb
================================================
<%= form.datetime_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/date_time/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/date_time/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/decimal/_form.html.erb
================================================
<%= form.number_field field.attribute_name, step: :any, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/decimal/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/decimal/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/enum/_form.html.erb
================================================
<%= form.select field.attribute_name, field.options_for_select(record), { prompt: true }, { data: { controller: "select" } } %>



================================================
FILE: app/views/madmin/fields/enum/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/enum/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/file/_form.html.erb
================================================
<%= form.file_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/file/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/file/_show.html.erb
================================================
<% value = field.value(record) %>
<% if value.respond_to?(:url) %>
  <%= link_to value.url, value.url, target: :_blank, class: "text-blue-500 underline" %>
<% else %>
  <%= value %>
<% end %>



================================================
FILE: app/views/madmin/fields/float/_form.html.erb
================================================
<%= form.number_field field.attribute_name, step: :any, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/float/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/float/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/has_many/_form.html.erb
================================================
<%= form.select "#{field.attribute_name.to_s.singularize}_ids", field.options_for_select(record), { prompt: true }, { multiple: true, data: { controller: "select", select_url_value: field.index_path } } %>



================================================
FILE: app/views/madmin/fields/has_many/_index.html.erb
================================================
<%= pluralize field.value(record).count, field.attribute_name.to_s %>



================================================
FILE: app/views/madmin/fields/has_many/_show.html.erb
================================================
<% pagy, records = field.paginated_value(record, params) %>
<% records.each do |object| %>
  <div>
    <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object), class: "text-blue-500 underline" %>
  </div>
<% end %>

<div class="pagination">
  <%== pagy_nav pagy if pagy.pages > 1 %>
  <span>Showing <%= tag.strong pagy.in %> of <%= tag.strong pagy.count %></span>
</div>



================================================
FILE: app/views/madmin/fields/has_one/_form.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/has_one/_index.html.erb
================================================
<% if (object = field.value(record)) %>
  <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object) %>
<% end %>



================================================
FILE: app/views/madmin/fields/has_one/_show.html.erb
================================================
<% if (object = field.value(record)) %>
  <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object) %>
<% end %>



================================================
FILE: app/views/madmin/fields/integer/_form.html.erb
================================================
<%= form.number_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/integer/_index.html.erb
================================================
<%= link_to_if field.attribute_name.to_s == resource.model.primary_key, field.value(record), resource.show_path(record) %>



================================================
FILE: app/views/madmin/fields/integer/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/json/_form.html.erb
================================================
<%= form.text_area field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/json/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/json/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/nested_has_many/_fields.html.erb
================================================
<%= content_tag :div, class: "nested-fields border border-gray-200 rounded-lg p-5", data: { new_record: f.object.new_record? } do %>
  <% field.nested_attributes.each do |name, nested_attribute| %>
    <% nested_field = nested_attribute.field %>
    <% next if nested_field.nil? %>
    <% next unless nested_field.visible?(action_name) %>
    <% next unless nested_field.visible?(:form) %>

    <div class="mb-4 flex">
      <%= render partial: nested_field.to_partial_path("form"), locals: { field: nested_field, record: f.object, form: f, resource: field.resource } %>
    </div>
  <% end %>

  <small><%= link_to "Remove", "#", data: { action: "click->nested-form#remove_association" } %></small>

  <%= f.hidden_field :_destroy %>
<% end %>



================================================
FILE: app/views/madmin/fields/nested_has_many/_form.html.erb
================================================
<div class="container space-y-8" data-controller="nested-form">
  <template data-target="nested-form.template">

    <%= form.fields_for field.attribute_name, field.to_model.new, child_index: 'NEW_RECORD' do |nested_form| %>
      <%= render(
        partial: field.to_partial_path('fields'),
        locals: {
          f: nested_form,
          field: field
        }
      ) %>
  <% end %>
  </template>

    <%= form.fields_for field.attribute_name do |nested_form| %>
      <%= render(
        partial: field.to_partial_path('fields'),
        locals: {
          f: nested_form,
          field: field
        }
      ) %>
  <% end %>

  <%= content_tag :div, class: '', data: { target:"nested-form.links" } do %>
    <%= link_to "+ Add new", "#",  data: { action: "click->nested-form#add_association" } %>
  <% end %>
</div>



================================================
FILE: app/views/madmin/fields/nested_has_many/_index.html.erb
================================================
<%= pluralize field.value(record).count, field.attribute_name.to_s %>



================================================
FILE: app/views/madmin/fields/nested_has_many/_show.html.erb
================================================
<% pagy, records = field.paginated_value(record, params) %>
<% records.each do |object| %>
  <div>
    <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object), class: "text-blue-500 underline" %>
  </div>
<% end %>

<div class="pagination">
  <%== pagy_nav pagy if pagy.pages > 1 %>
  <span>Showing <%= tag.strong pagy.in %> of <%= tag.strong pagy.count %></span>
</div>



================================================
FILE: app/views/madmin/fields/password/_form.html.erb
================================================
<%= form.password_field field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/password/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/password/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/polymorphic/_form.html.erb
================================================
<%= form.fields_for field.attribute_name do |pf| %>
  <%= pf.select :value, field.options_for_select(record).map(&:to_global_id), { selected: field.value(record)&.to_global_id, prompt: true }, { data: { controller: "select" } } %>
  <%= pf.hidden_field :type, value: "polymorphic" %>
<% end %>



================================================
FILE: app/views/madmin/fields/polymorphic/_index.html.erb
================================================
<% if (object = field.value(record)) %>
  <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object) %>
<% end %>



================================================
FILE: app/views/madmin/fields/polymorphic/_show.html.erb
================================================
<% if (object = field.value(record)) %>
  <%= link_to Madmin.resource_for(object).display_name(object), Madmin.resource_for(object).show_path(object), class: "text-blue-500 underline" %>
<% end %>



================================================
FILE: app/views/madmin/fields/rich_text/_form.html.erb
================================================
<%= form.rich_text_area field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/rich_text/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/rich_text/_show.html.erb
================================================
<div class="prose">
  <%= field.value(record) %>
</div>



================================================
FILE: app/views/madmin/fields/select/_form.html.erb
================================================
<%= form.select field.attribute_name, field.options_for_select(record), { prompt: true }, { data: { controller: "select" } } %>



================================================
FILE: app/views/madmin/fields/select/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/select/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/string/_form.html.erb
================================================
<%= form.text_field field.attribute_name, class: "form-input", placeholder: field.options.placeholder %>



================================================
FILE: app/views/madmin/fields/string/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/string/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/text/_form.html.erb
================================================
<%= form.text_area field.attribute_name, class: "form-input" %>



================================================
FILE: app/views/madmin/fields/text/_index.html.erb
================================================
<%= truncate field.value(record).to_s %>



================================================
FILE: app/views/madmin/fields/text/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/time/_form.html.erb
================================================
<div class="block md:inline-block md:w-32 flex-shrink-0 text-gray-700">
  <%= render "madmin/shared/label", form: form, field: field %>
</div>
<%= form.time_select field.attribute_name, {}, { class: "form-select" } %>



================================================
FILE: app/views/madmin/fields/time/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/fields/time/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: app/views/madmin/shared/_label.html.erb
================================================
<%= form.label field.attribute_name, field.options.label %>
<% if field.required? %>
  <span class="required">*</span>
<% end %>



================================================
FILE: config/importmap.rb
================================================
pin "application", to: "madmin/application.js", preload: true
pin "@hotwired/turbo-rails", to: "turbo.min.js", preload: true
pin "@hotwired/stimulus", to: "stimulus.min.js", preload: true
pin "@hotwired/stimulus-loading", to: "stimulus-loading.js", preload: true
pin "trix"
pin "@rails/actiontext", to: "actiontext.esm.js"
pin_all_from Madmin::Engine.root.join("app/javascript/madmin/controllers"), under: "controllers", to: "madmin/controllers"

pin "tom-select", to: "https://ga.jspm.io/npm:tom-select@2.4.1/dist/js/tom-select.complete.js"
pin "tailwindcss-stimulus-components", to: "https://ga.jspm.io/npm:tailwindcss-stimulus-components@6.1.2/dist/tailwindcss-stimulus-components.module.js"



================================================
FILE: docs/assets.md
================================================
## Adding CSS From Your Rails Application

Madmin exposes an attribute `Madmin.stylesheets` which is an Array of stylesheet names. The default 
value of this attribute is simply the stock `madmin/application` CSS stylesheet.

```ruby
Madmin.stylesheets << "my_stylesheet"
```
The above will include `app/assets/stylesheets/my_stylesheet.css` from your Rails app in the Madmin layout.

## Adding CSS From A Gem

If you need to add custom stylesheets to, for example, include the CSS stylesheet for Hotwire 
Combobox you can do so in an initializer like so:

```ruby
Madmin.stylesheets << "hotwire_combobox"
```
The above will include `app/assets/stylesheets/hotwire_combobox` in the Madmin layout.

## Adding JavaScript From Your Rails Application

If you need to add a package you can do so by accessing `Madmin.importmap.draw` passing in the file
containing your importmap line. For example, to add the Hotwire Combobox importmap, you can do so in your
Madmin initializer like so:

```ruby
Madmin.importmap.draw HotwireCombobox::Engine.root.join("config/hw_importmap.rb")
```

## Complete Asset Override

To fully customize all assets, you can create the following file in your Rails application
`app/views/madmin/_javascript.html.erb`, adding the `javascript_importmap_tags` and `stylesheet_link_tag`
you need.



================================================
FILE: docs/authentication.md
================================================
# Authenticating Madmin

There are a few different ways of adding authentication to Madmin.

### before_action

In `app/controllers/madmin/application_controller.rb`, there is a placeholder `before_action` that can be used for authenticating requests.

```ruby
module Madmin
  class ApplicationController < Madmin::BaseController
    include Rails.application.routes.url_helpers

    before_action :authenticate_admin_user

    def authenticate_admin_user
      # TODO: Add your authentication logic here

      # For example, with Rails 8 authentication
      # redirect_to "/", alert: "Not authorized." unless authenticated? && Current.user.admin?

      # Or with Devise
      # redirect_to "/", alert: "Not authorized." unless current_user&.admin?
    end
  end
end
```

### Devise Routes

Wrap the madmin routes in an `authenticated` or `authenticate` block:

```
authenticated :user, lambda { |u| u.admin? } do
  namespace :madmin do
  end
end
```

We recommend using `authenticated` as the `/madmin` route will 404 for any non-admins and reduces malicious attempts to get into your admin area.

### HTTP Basic Authentication

If you want to use HTTP Basic Authentication, you can use this in your
`Madmin::ApplicationController`:

```ruby
module Madmin
  class ApplicationController < Madmin::BaseController
    http_basic_authenticate_with(
      name: ENV['ADMIN_USERNAME'] || Rails.application.credentials.admin_username,
      password: ENV['ADMIN_PASSWORD'] || Rails.application.credentials.admin_password
    )
  end
end
```

This will use ENV vars (if defined) or fallback to the Rails credentials for admin username and password.

## Testing Authentication

We recommend writing an integration test to ensure only admins have access to Madmin. Something like this (assuming you have fixtures for regular users and admins).

```ruby
require "test_helper"

class MadminTest < ActionDispatch::IntegrationTest
  test "guests cannot access madmin" do
    get madmin_path
    assert_response :unauthorized
  end

  test "regular users cannot access madmin" do
    sign_in users(:regular)
    get madmin_path
    assert_response :unauthorized
  end

  test "admins can access madmin" do
    sign_in users(:admin)
    get madmin_path
    assert_response :success
  end
end
```



================================================
FILE: docs/controllers.md
================================================
# Customizing Controllers

When you generate a resource, it will generate a matching controller that you can override as needed.

For example `rails g madmin:resource Post` generates `app/controllers/madmin/posts_controller.rb`.

Refer to the resources_controller to what is accessible and how the normal actions look: [resource_controller.rb](../app/controllers/madmin/resource_controller.rb)



================================================
FILE: docs/custom_fields.md
================================================
## Custom Fields
You can generate a custom field with:

```bash
rails g madmin:field Custom
```

This will create a CustomField class in app/madmin/fields/custom_field.rb And the related views:

```bash
# -> app/views/madmin/fields/custom_field/_form.html.erb
# -> app/views/madmin/fields/custom_field/_index.html.erb
# -> app/views/madmin/fields/custom_field/_show.html.erb
```

You can then use this field on our resource:

```ruby
class PostResource < Madmin::Resource
  attribute :title, field: CustomField
end
```



================================================
FILE: docs/resources.md
================================================
# Resources
Madmin uses Resource classes to add models to the admin area.

## Generate a Resource
To generate a resource for a model, you can run:

rails g madmin:resource ActionText::RichText



================================================
FILE: docs/routes.md
================================================
# Routes
Routes should be under the namespace of madmin module:

```ruby
namespace :madmin do
  resources :posts
  namespace :user do
    resources :connected_accounts
  end	
end
```



================================================
FILE: docs/views.md
================================================
# Configuring Views
The views packaged within the gem are a great starting point, but inevitably people will need to be able to customize those views.

You can use the included generator to create the appropriate view files, which can then be customized.

For example, running the following will copy over all of the views into your application that will be used for every resource:

```bash
rails generate madmin:views
```



================================================
FILE: gemfiles/rails_7.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3", "~> 1.4"
gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"
gem "rails", "~> 7.0.0"
gem "benchmark"
gem "drb"
gem "mutex_m"
gem "concurrent-ruby", "< 1.3.5"

gemspec path: "../"



================================================
FILE: gemfiles/rails_7.gemfile.lock
================================================
PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.8.7)
      actionpack (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.8.7)
      actionpack (= 7.0.8.7)
      activejob (= 7.0.8.7)
      activerecord (= 7.0.8.7)
      activestorage (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.8.7)
      actionpack (= 7.0.8.7)
      actionview (= 7.0.8.7)
      activejob (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.8.7)
      actionview (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.8.7)
      actionpack (= 7.0.8.7)
      activerecord (= 7.0.8.7)
      activestorage (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.8.7)
      activesupport (= 7.0.8.7)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (7.0.8.7)
      activesupport (= 7.0.8.7)
      globalid (>= 0.3.6)
    activemodel (7.0.8.7)
      activesupport (= 7.0.8.7)
    activerecord (7.0.8.7)
      activemodel (= 7.0.8.7)
      activesupport (= 7.0.8.7)
    activestorage (7.0.8.7)
      actionpack (= 7.0.8.7)
      activejob (= 7.0.8.7)
      activerecord (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.8.7)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.4)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    method_source (1.1.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mutex_m (0.3.0)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (7.0.8.7)
      actioncable (= 7.0.8.7)
      actionmailbox (= 7.0.8.7)
      actionmailer (= 7.0.8.7)
      actionpack (= 7.0.8.7)
      actiontext (= 7.0.8.7)
      actionview (= 7.0.8.7)
      activejob (= 7.0.8.7)
      activemodel (= 7.0.8.7)
      activerecord (= 7.0.8.7)
      activestorage (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      bundler (>= 1.15.0)
      railties (= 7.0.8.7)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.0.8.7)
      actionpack (= 7.0.8.7)
      activesupport (= 7.0.8.7)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.3.0)
    regexp_parser (2.10.0)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    sqlite3 (1.7.3)
      mini_portile2 (~> 2.8.0)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.12)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-21
  arm64-darwin-22
  arm64-darwin-23
  ruby
  x86_64-darwin-20
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  benchmark
  concurrent-ruby (< 1.3.5)
  drb
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mutex_m
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  propshaft
  puma
  rails (~> 7.0.0)
  sqlite3 (~> 1.4)
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.5.18



================================================
FILE: gemfiles/rails_7_1.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3", "~> 1.4"
gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"
gem "rails", "~> 7.1.0"

gemspec path: "../"



================================================
FILE: gemfiles/rails_7_1.gemfile.lock
================================================
PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.5.1)
      actionpack (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.5.1)
      actionview (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.5.1)
      actionpack (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.5.1)
      activesupport (= 7.1.5.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.1.5.1)
      activesupport (= 7.1.5.1)
      globalid (>= 0.3.6)
    activemodel (7.1.5.1)
      activesupport (= 7.1.5.1)
    activerecord (7.1.5.1)
      activemodel (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      timeout (>= 0.4.0)
    activestorage (7.1.5.1)
      actionpack (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      marcel (~> 1.0)
    activesupport (7.1.5.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mutex_m (0.3.0)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (7.1.5.1)
      actioncable (= 7.1.5.1)
      actionmailbox (= 7.1.5.1)
      actionmailer (= 7.1.5.1)
      actionpack (= 7.1.5.1)
      actiontext (= 7.1.5.1)
      actionview (= 7.1.5.1)
      activejob (= 7.1.5.1)
      activemodel (= 7.1.5.1)
      activerecord (= 7.1.5.1)
      activestorage (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      bundler (>= 1.15.0)
      railties (= 7.1.5.1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.1.5.1)
      actionpack (= 7.1.5.1)
      activesupport (= 7.1.5.1)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sqlite3 (1.7.3)
      mini_portile2 (~> 2.8.0)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-21
  arm64-darwin-22
  arm64-darwin-23
  ruby
  x86_64-darwin-20
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  propshaft
  puma
  rails (~> 7.1.0)
  sqlite3 (~> 1.4)
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.6.8



================================================
FILE: gemfiles/rails_7_2.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3"
gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"
gem "rails", "~> 7.2.0.beta2"

gemspec path: "../"



================================================
FILE: gemfiles/rails_7_2.gemfile.lock
================================================
PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.2.2.1)
      actionpack (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.2.2.1)
      actionpack (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      mail (>= 2.8.0)
    actionmailer (7.2.2.1)
      actionpack (= 7.2.2.1)
      actionview (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (7.2.2.1)
      actionview (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (7.2.2.1)
      actionpack (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.2.2.1)
      activesupport (= 7.2.2.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.2.2.1)
      activesupport (= 7.2.2.1)
      globalid (>= 0.3.6)
    activemodel (7.2.2.1)
      activesupport (= 7.2.2.1)
    activerecord (7.2.2.1)
      activemodel (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      timeout (>= 0.4.0)
    activestorage (7.2.2.1)
      actionpack (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      marcel (~> 1.0)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (7.2.2.1)
      actioncable (= 7.2.2.1)
      actionmailbox (= 7.2.2.1)
      actionmailer (= 7.2.2.1)
      actionpack (= 7.2.2.1)
      actiontext (= 7.2.2.1)
      actionview (= 7.2.2.1)
      activejob (= 7.2.2.1)
      activemodel (= 7.2.2.1)
      activerecord (= 7.2.2.1)
      activestorage (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      bundler (>= 1.15.0)
      railties (= 7.2.2.1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.2.2.1)
      actionpack (= 7.2.2.1)
      activesupport (= 7.2.2.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sqlite3 (2.7.0-aarch64-linux-gnu)
    sqlite3 (2.7.0-arm-linux-gnu)
    sqlite3 (2.7.0-arm64-darwin)
    sqlite3 (2.7.0-x86-linux-gnu)
    sqlite3 (2.7.0-x86_64-darwin)
    sqlite3 (2.7.0-x86_64-linux-gnu)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  propshaft
  puma
  rails (~> 7.2.0.beta2)
  sqlite3
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.6.8



================================================
FILE: gemfiles/rails_8_0.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3"
gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"
gem "rails", "~> 8.0.0"

gemspec path: "../"



================================================
FILE: gemfiles/rails_8_0.gemfile.lock
================================================
PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    minitest (5.25.5)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sqlite3 (2.7.0-arm64-darwin)
    sqlite3 (2.7.0-x86_64-linux-gnu)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  propshaft
  puma
  rails (~> 8.0.0)
  sqlite3
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.6.8



================================================
FILE: gemfiles/rails_main.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3"
gem "propshaft"
gem "turbo-rails"
gem "stimulus-rails"
gem "rails", git: "https://github.com/rails/rails.git"

gemspec path: "../"



================================================
FILE: gemfiles/rails_main.gemfile.lock
================================================
GIT
  remote: https://github.com/rails/rails.git
  revision: b51b272b9b110c9d1678fef1caba6e392c667018
  specs:
    actioncable (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      mail (>= 2.8.0)
    actionmailer (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.1.0.alpha)
      action_text-trix (~> 2.1.15)
      actionpack (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      globalid (>= 0.3.6)
    activemodel (8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
    activerecord (8.1.0.alpha)
      activemodel (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      timeout (>= 0.4.0)
    activestorage (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      marcel (~> 1.0)
    activesupport (8.1.0.alpha)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    rails (8.1.0.alpha)
      actioncable (= 8.1.0.alpha)
      actionmailbox (= 8.1.0.alpha)
      actionmailer (= 8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      actiontext (= 8.1.0.alpha)
      actionview (= 8.1.0.alpha)
      activejob (= 8.1.0.alpha)
      activemodel (= 8.1.0.alpha)
      activerecord (= 8.1.0.alpha)
      activestorage (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      bundler (>= 1.15.0)
      railties (= 8.1.0.alpha)
    railties (8.1.0.alpha)
      actionpack (= 8.1.0.alpha)
      activesupport (= 8.1.0.alpha)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)

PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    action_text-trix (2.1.15)
      railties
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sqlite3 (2.7.0)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.7.0-arm64-darwin)
    sqlite3 (2.7.0-x86_64-darwin)
    sqlite3 (2.7.0-x86_64-linux-gnu)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-21
  arm64-darwin-22
  arm64-darwin-23
  ruby
  x86_64-darwin-20
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  propshaft
  puma
  rails!
  sqlite3
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.6.8



================================================
FILE: gemfiles/sprockets.gemfile
================================================
# This file was generated by Appraisal

source "https://rubygems.org"

gem "appraisal"
gem "bcrypt"
gem "ffaker", "~> 2.17"
gem "friendly_id", "~> 5.4"
gem "name_of_person", "~> 1.1", ">= 1.1.1"
gem "puma"
gem "standardrb"
gem "web-console", group: :development
gem "pg"
gem "mysql2"
gem "sqlite3"
gem "turbo-rails"
gem "stimulus-rails"
gem "sprockets-rails"

gemspec path: "../"



================================================
FILE: gemfiles/sprockets.gemfile.lock
================================================
PATH
  remote: ..
  specs:
    madmin (2.0.4)
      importmap-rails
      pagy (>= 3.5)
      propshaft
      rails (>= 7.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    appraisal (2.5.0)
      bundler
      rake
      thor (>= 0.14.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    builder (3.3.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    ffaker (2.24.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_mime (1.1.5)
    minitest (5.25.5)
    mysql2 (0.5.6)
    name_of_person (1.1.3)
      activesupport (>= 5.2.0)
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    securerandom (0.4.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (2.7.0-arm64-darwin)
    sqlite3 (2.7.0-x86_64-darwin)
    sqlite3 (2.7.0-x86_64-linux-gnu)
    standard (1.50.0)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.75.5)
      standard-custom (~> 1.0.0)
      standard-performance (~> 1.8)
    standard-custom (1.0.2)
      lint_roller (~> 1.0)
      rubocop (~> 1.50)
    standard-performance (1.8.0)
      lint_roller (~> 1.1)
      rubocop-performance (~> 1.25.0)
    standardrb (1.0.1)
      standard
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    thor (1.3.2)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-24
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  appraisal
  bcrypt
  ffaker (~> 2.17)
  friendly_id (~> 5.4)
  madmin!
  mysql2
  name_of_person (~> 1.1, >= 1.1.1)
  pg
  puma
  sprockets-rails
  sqlite3
  standardrb
  stimulus-rails
  turbo-rails
  web-console

BUNDLED WITH
   2.6.8



================================================
FILE: lib/madmin.rb
================================================
require "madmin/engine"

require "pagy"

module Madmin
  autoload :Field, "madmin/field"
  autoload :GeneratorHelpers, "madmin/generator_helpers"
  autoload :Menu, "madmin/menu"
  autoload :Resource, "madmin/resource"
  autoload :ResourceBuilder, "madmin/resource_builder"
  autoload :Search, "madmin/search"

  module Fields
    autoload :Attachment, "madmin/fields/attachment"
    autoload :Attachments, "madmin/fields/attachments"
    autoload :BelongsTo, "madmin/fields/belongs_to"
    autoload :Boolean, "madmin/fields/boolean"
    autoload :Currency, "madmin/fields/currency"
    autoload :Date, "madmin/fields/date"
    autoload :DateTime, "madmin/fields/date_time"
    autoload :Decimal, "madmin/fields/decimal"
    autoload :Enum, "madmin/fields/enum"
    autoload :File, "madmin/fields/file"
    autoload :Float, "madmin/fields/float"
    autoload :HasMany, "madmin/fields/has_many"
    autoload :HasOne, "madmin/fields/has_one"
    autoload :Integer, "madmin/fields/integer"
    autoload :Json, "madmin/fields/json"
    autoload :NestedHasMany, "madmin/fields/nested_has_many"
    autoload :Password, "madmin/fields/password"
    autoload :Polymorphic, "madmin/fields/polymorphic"
    autoload :RichText, "madmin/fields/rich_text"
    autoload :Select, "madmin/fields/select"
    autoload :String, "madmin/fields/string"
    autoload :Text, "madmin/fields/text"
    autoload :Time, "madmin/fields/time"
  end

  mattr_accessor :importmap, default: Importmap::Map.new
  mattr_accessor :menu, default: Menu.new
  mattr_accessor :site_name
  mattr_accessor :stylesheets, default: []

  class << self
    def resource_for(object)
      if object.is_a? ::ActiveStorage::Attached
        "ActiveStorage::AttachmentResource".constantize
      else
        begin
          "#{object.class.name}Resource".constantize
        rescue
          # For STI models, see if there's a superclass resource available
          if (column = object.class.inheritance_column) && object.class.column_names.include?(column)
            "#{object.class.superclass.base_class.name}Resource".constantize
          else
            raise
          end
        end
      end
    end

    def resource_by_name(name)
      "#{name}Resource".constantize
    end

    def resources
      @resources ||= resource_names.map(&:constantize)
    end

    def reset_resources!
      @resources = nil
      menu.reset
    end

    def resource_names
      root = Rails.root.join("app/madmin/resources/")
      files = Dir.glob(root.join("**/*.rb"))
      files.sort!.map! { |f| f.split(root.to_s).last.delete_suffix(".rb").classify }
    end
  end
end



================================================
FILE: lib/generators/madmin/field/field_generator.rb
================================================
module Madmin
  module Generators
    class FieldGenerator < Rails::Generators::NamedBase
      include Madmin::GeneratorHelpers

      source_root File.expand_path("../templates", __FILE__)

      def eager_load
        Rails.application.eager_load!
      end

      def generate_field
        template "field.rb", "app/madmin/fields/#{file_path}_field.rb"
        copy_resource_template "_form"
        copy_resource_template "_index"
        copy_resource_template "_show"
      end

      private

      def copy_resource_template(template_name)
        template_file = "#{template_name}.html.erb"

        copy_file(
          template_file,
          "app/views/madmin/fields/#{file_path}_field/#{template_file}"
        )
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/field/templates/_form.html.erb
================================================
<%= form.text_field field.attribute_name, class: "form-input" %>



================================================
FILE: lib/generators/madmin/field/templates/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: lib/generators/madmin/field/templates/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: lib/generators/madmin/field/templates/field.rb.tt
================================================
class <%= class_name %>Field < Madmin::Field
  # def value(record)
  #   record.public_send(attribute_name)
  # end

  # def to_partial_path(name)
  #   unless %w[index show form].include? name
  #     raise ArgumentError, "`partial` must be 'index', 'show', or 'form'"
  #  end
  #
  #   "/madmin/fields/#{self.class.field_type}/#{name}"
  # end

  # def to_param
  #   attribute_name
  # end

  # # Used for checking visibility of attribute on an view
  # def visible?(action, default: true)
  #   options.fetch(action.to_sym, default)
  # end

  # def required?
  #   model.validators_on(attribute_name).any? { |v| v.is_a? ActiveModel::Validations::PresenceValidator }
  # end
end



================================================
FILE: lib/generators/madmin/install/install_generator.rb
================================================
require "madmin/generator_helpers"

module Madmin
  module Generators
    class InstallGenerator < ::Rails::Generators::Base
      include Madmin::GeneratorHelpers

      source_root File.expand_path("../templates", __FILE__)

      def eager_load
        Rails.application.eager_load!
      end

      def copy_controller
        template("controller.rb.tt", "app/controllers/madmin/application_controller.rb")
      end

      def generate_routes
        if rails6_1_and_up?
          route "draw :madmin", file: ROUTES_FILE[:default]
          template("routes.rb.tt", "config/routes/madmin.rb")
        end

        if route_namespace_exists?
          route "root to: \"dashboard#show\"", indentation: separated_routes_file? ? 2 : 4, sentinel: /namespace :madmin do\s*\n/m
        else
          route "root to: \"dashboard#show\"", namespace: [:madmin]
        end
      end

      def generate_resources
        generateable_models.each do |model|
          if model.table_exists?
            call_generator "madmin:resource", model.to_s
          else
            puts "Skipping #{model} because database table does not exist"
          end
        end
      end

      private

      # Skip Abstract classes, ActiveRecord::Base, and auto-generated HABTM models
      def generateable_models
        active_record_models.reject do |model|
          model.abstract_class? || model == ::ActiveRecord::Base || model.name.start_with?("HABTM_")
        end
      end

      def active_record_models
        ObjectSpace.each_object(::ActiveRecord::Base.singleton_class)
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/install/templates/controller.rb.tt
================================================
module Madmin
  class ApplicationController < Madmin::BaseController
    before_action :authenticate_admin_user

    def authenticate_admin_user
      # TODO: Add your authentication logic here

      # For example, with Rails 8 authentication
      # redirect_to "/", alert: "Not authorized." unless authenticated? && Current.user.admin?

      # Or with Devise
      # redirect_to "/", alert: "Not authorized." unless current_user&.admin?
    end
  end
end



================================================
FILE: lib/generators/madmin/install/templates/routes.rb.tt
================================================
# Below are the routes for madmin
namespace :madmin do
end



================================================
FILE: lib/generators/madmin/resource/resource_generator.rb
================================================
module Madmin
  module Generators
    class ResourceGenerator < Rails::Generators::NamedBase
      include Madmin::GeneratorHelpers

      source_root File.expand_path("../templates", __FILE__)

      def eager_load
        Rails.application.eager_load!
      end

      def generate_resource
        template "resource.rb", "app/madmin/resources/#{file_path}_resource.rb"
      end

      def generate_controller
        destination = Rails.root.join("app/controllers/madmin/#{file_path.pluralize}_controller.rb")
        template("controller.rb", destination)
      end

      def generate_route
        if route_namespace_exists?
          route "resources :#{plural_name}", namespace: class_path, indentation: separated_routes_file? ? 2 : 4, sentinel: /namespace :madmin[^\n]*do\s*\n/m
        else
          route "resources :#{plural_name}", namespace: [:madmin] + class_path
        end
      end

      private

      def model
        @model ||= class_name.constantize
      end

      def resource_builder
        @resource_builder ||= ResourceBuilder.new(model)
      end

      def model_attributes
        resource_builder.attributes
      end

      delegate :associations, :virtual_attributes, :store_accessors, to: :resource_builder

      def formatted_options_for_attribute(name)
        options = options_for_attribute(name)
        return if options.blank?

        ", " + options.map { |key, value|
          "#{key}: #{value}"
        }.join(", ")
      end

      def options_for_attribute(name)
        if %w[id created_at updated_at].include?(name)
          {form: false}

        # has_secure_passwords should only show on forms
        elsif name.ends_with?("_confirmation") || virtual_attributes.include?("#{name}_confirmation")
          {index: false, show: false}

        # Counter cache columns are typically not editable
        elsif name.ends_with?("_count")
          {form: false}

        # Attributes without a database column
        elsif !model.column_names.include?(name) && !store_accessors.map(&:to_s).include?(name)
          {index: false}
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/resource/templates/controller.rb.tt
================================================
module Madmin
  class <%= class_name.pluralize %>Controller < Madmin::ResourceController
<% if class_name == "ActiveStorage::Blob" -%>
    def new
      super
      @record.assign_attributes(filename: "")
    end
<% end -%>
  end
end



================================================
FILE: lib/generators/madmin/resource/templates/resource.rb.tt
================================================
class <%= class_name %>Resource < Madmin::Resource
  # Attributes
<% model_attributes.each do |attribute_name| -%>
  attribute :<%= attribute_name %><%= formatted_options_for_attribute(attribute_name) %>
<% end -%>

  # Associations
<% associations.each do |association_name| -%>
  attribute :<%= association_name %>
<% end -%>

  # Add scopes to easily filter records
  # scope :published

  # Add actions to the resource's show page
  # member_action do |record|
  #   link_to "Do Something", some_path
  # end

  # Customize the display name of records in the admin area.
  # def self.display_name(record) = record.name

  # Customize the default sort column and direction.
  # def self.default_sort_column = "created_at"
  #
  # def self.default_sort_direction = "desc"
end



================================================
FILE: lib/generators/madmin/views/edit_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class EditGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_edit
          copy_resource_template("edit")
          copy_resource_template("_form")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/form_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class FormGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_form
          copy_resource_template("_form")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/index_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class IndexGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_template
          copy_resource_template("index")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/javascript_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class JavascriptGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_navigation
          copy_resource_template("_javascript")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/layout_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class LayoutGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_template
          copy_file(
            "../../layouts/madmin/application.html.erb",
            "app/views/layouts/madmin/application.html.erb"
          )

          call_generator("madmin:views:navigation")
          copy_resource_template("_javascript")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/navigation_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class NavigationGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_navigation
          copy_resource_template("_navigation")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/new_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class NewGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_new
          copy_resource_template("new")
          copy_resource_template("_form")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/show_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    module Views
      class ShowGenerator < Madmin::ViewGenerator
        source_root template_source_path

        def copy_template
          copy_resource_template("show")
        end
      end
    end
  end
end



================================================
FILE: lib/generators/madmin/views/views_generator.rb
================================================
require "madmin/view_generator"

module Madmin
  module Generators
    class ViewsGenerator < Madmin::ViewGenerator
      def copy_templates
        # Some generators duplicate templates, so not everything is present here
        call_generator("madmin:views:edit", resource_path, "--namespace", namespace)
        call_generator("madmin:views:index", resource_path, "--namespace", namespace)
        call_generator("madmin:views:layout", resource_path, "--namespace", namespace)
        call_generator("madmin:views:new", resource_path, "--namespace", namespace)
        call_generator("madmin:views:show", resource_path, "--namespace", namespace)
      end
    end
  end
end



================================================
FILE: lib/madmin/engine.rb
================================================
require "importmap-rails"

module Madmin
  class Engine < ::Rails::Engine
    isolate_namespace Madmin

    config.before_configuration do |app|
      app.config.eager_load_paths << File.expand_path("app/madmin/resources", Rails.root)
      app.config.eager_load_paths << File.expand_path("app/madmin/fields", Rails.root)
    end

    config.to_prepare do
      Madmin.reset_resources!
      Madmin.site_name ||= Rails.application.class.module_parent_name
    end

    initializer "madmin.assets" do |app|
      if app.config.respond_to?(:assets)
        app.config.assets.paths << root.join("app/assets/stylesheets")
        app.config.assets.paths << root.join("app/javascript")
        app.config.assets.precompile += %w[madmin_manifest]

        Madmin.stylesheets << if defined?(::Sprockets)
          "madmin/application-sprockets"
        else
          "madmin/application"
        end
      end
    end

    initializer "madmin.importmap", before: "importmap" do |app|
      Madmin.importmap.draw root.join("config/importmap.rb")
      Madmin.importmap.cache_sweeper watches: root.join("app/javascript")

      ActiveSupport.on_load(:action_controller_base) do
        before_action { Madmin.importmap.cache_sweeper.execute_if_updated }
      end
    end
  end
end



================================================
FILE: lib/madmin/field.rb
================================================
module Madmin
  class Field
    attr_reader :attribute_name, :model, :options, :resource

    def self.field_type
      to_s.split("::").last.underscore
    end

    def initialize(attribute_name:, model:, resource:, options:)
      @attribute_name = attribute_name.to_sym
      @model = model
      @resource = resource
      @options = options
    end

    def value(record)
      record.try(attribute_name)
    end

    def to_partial_path(name)
      unless %w[index show form].include? name.to_s
        raise ArgumentError, "`partial` must be 'index', 'show', or 'form'"
      end

      "/madmin/fields/#{self.class.field_type}/#{name}"
    end

    def to_param
      attribute_name
    end

    # Used for checking visibility of attribute on an view
    def visible?(action)
      action = action.to_sym
      options.fetch(action) do
        case action
        when :index
          default_index_attributes.include?(attribute_name)
        else
          true
        end
      end
    end

    def default_index_attributes
      [model.primary_key.to_sym, :avatar, :title, :name, :user, :created_at]
    end

    def required?
      model.validators_on(attribute_name).any? { |v| v.is_a? ActiveModel::Validations::PresenceValidator }
    end

    def searchable?
      false
    end

    def paginateable?
      false
    end
  end
end



================================================
FILE: lib/madmin/generator_helpers.rb
================================================
module Madmin
  module GeneratorHelpers
    ROUTES_FILE = {default: "config/routes.rb", separated: "config/routes/madmin.rb"}.freeze

    def call_generator(generator, *args)
      Rails::Generators.invoke(generator, args, generator_options)
    end

    def route_namespace_exists?
      File.readlines(Rails.root.join(default_routes_file)).grep(/namespace :madmin/).size > 0
    end

    def rails6_1_and_up?
      Gem.loaded_specs["rails"].version >= Gem::Version.new(6.1)
    end

    # Method copied from Rails 6.1 master
    def route(routing_code, namespace: nil, sentinel: nil, indentation: 2, file: default_routes_file)
      routing_code = Array(namespace).reverse.reduce(routing_code) { |code, ns|
        "namespace :#{ns} do\n#{indent(code, 2)}\nend"
      }

      log :route, routing_code
      sentinel ||= default_sentinel(file)

      in_root do
        inject_into_file file, optimize_indentation(routing_code, indentation), after: sentinel, verbose: false, force: false
      end
    end

    # Method copied from Rails 6.1 master
    def optimize_indentation(value, amount = 0)
      return "#{value}\n" unless value.is_a?(String)
      "#{value.strip_heredoc.indent(amount).chomp}\n"
    end

    private

    def separated_routes_file?
      default_routes_file.eql?(ROUTES_FILE[:separated])
    end

    def default_sentinel(file)
      file.eql?(ROUTES_FILE[:default]) ? /\.routes\.draw do\s*\n/m : /namespace :madmin[^\n]*do\s*\n/m
    end

    def default_routes_file
      if rails6_1_and_up? && File.exist?(ROUTES_FILE[:separated])
        ROUTES_FILE[:separated]
      else
        ROUTES_FILE[:default]
      end
    end

    def generator_options
      {behavior: behavior}
    end
  end
end



================================================
FILE: lib/madmin/menu.rb
================================================
module Madmin
  class Menu
    def initialize
      @children = {}
    end

    def reset
      @children = {}
    end

    def before_render(&block)
      if block_given?
        @before_render = block
      else
        @before_render
      end
    end

    def render(&block)
      instance_eval(&@before_render) if @before_render

      # Ensure all the resources have been added to the menu
      Madmin.resources.each do |resource|
        next if resource.menu_options == false
        add resource.menu_options
      end

      items.each(&block)
    end

    module Node
      def add(options)
        options = options.dup

        if (parent = options.delete(:parent))
          @children[parent] ||= Item.new(label: parent)
          @children[parent].add options
        else
          item = Item.new(**options)
          @children[item.label] = item
        end
      end

      def items
        @children.values.sort do |a, b|
          result = a.position <=> b.position
          result = a.label <=> b.label if result == 0 # sort alphabetically for the same position
          result
        end
      end
    end

    include Node

    class Item
      include Node

      attr_reader :label, :url, :position, :parent, :children

      def initialize(label:, url: nil, position: 99, parent: nil, **options)
        @label = label
        @url = url
        @position = position
        @parent = parent
        @if = options.delete(:if)
        @children = {}
      end
    end
  end
end



================================================
FILE: lib/madmin/namespace.rb
================================================
module Madmin
  class Namespace
    def initialize(namespace)
      @namespace = namespace
    end

    def resources
      @resources ||= routes.map(&:first).uniq.map { |path|
        Resource.new(namespace, path)
      }
    end

    def routes
      @routes ||= all_routes.select { |controller, _action|
        controller.starts_with?("#{namespace}/")
      }.map { |controller, action|
        [controller.gsub(/^#{namespace}\//, ""), action]
      }
    end

    def resources_with_index_route
      routes.select { |_resource, route| route == "index" }.map(&:first).uniq
    end

    private

    attr_reader :namespace

    def all_routes
      Rails.application.routes.routes.map do |route|
        route.defaults.values_at(:controller, :action).map(&:to_s)
      end
    end
  end
end



================================================
FILE: lib/madmin/resource.rb
================================================
module Madmin
  class Resource
    Attribute = Data.define(:name, :type, :field)

    class_attribute :attributes, default: ActiveSupport::OrderedHash.new
    class_attribute :member_actions, default: []
    class_attribute :scopes, default: []
    class_attribute :menu_options, instance_reader: false

    class << self
      def inherited(base)
        base.attributes = attributes.dup
        base.member_actions = scopes.dup
        base.scopes = scopes.dup
        super
      end

      def model(value = nil)
        if value
          @model = value
        else
          @model ||= model_name.constantize
        end
      end

      def model_find(id)
        friendly_model? ? model.friendly.find(id) : model.find(id)
      end

      def model_name
        to_s.chomp("Resource").classify
      end

      def scope(name)
        scopes << name
      end

      def get_attribute(name)
        attributes[name]
      end

      def attribute(name, type = nil, **options)
        type ||= infer_type(name)
        field = options.delete(:field) || field_for_type(type)

        if field.nil?
          Rails.logger.warn <<~MESSAGE
            WARNING: Madmin could not infer a field type for `#{name}` attribute in `#{self.name}`. Defaulting to a String type.
            #{caller.find { _1.start_with? Rails.root.to_s }}
          MESSAGE
          field = Fields::String
        end

        config = ActiveSupport::OrderedOptions.new.merge(options)
        yield config if block_given?

        # Form is an alias for new & edit
        if config.has_key?(:form)
          config.new = config[:form]
          config.edit = config[:form]
        end

        # New/create and edit/update need to match
        config.create = config[:create] if config.has_key?(:new)
        config.update = config[:update] if config.has_key?(:edit)

        attributes[name] = Attribute.new(
          name: name,
          type: type,
          field: field.new(attribute_name: name, model: model, resource: self, options: config)
        )
      end

      def friendly_name
        model_name.gsub("::", " / ").split(/(?=[A-Z])/).join(" ")
      end

      # Support for isolated namespaces
      # Finds parent module class to include in polymorphic urls
      def route_namespace
        return @route_namespace if instance_variable_defined?(:@route_namespace)
        namespace = model.module_parents.detect do |n|
          n.respond_to?(:use_relative_model_naming?) && n.use_relative_model_naming?
        end
        @route_namespace = (namespace ? namespace.name.singularize.underscore.to_sym : nil)
      end

      def index_path(options = {})
        url_helpers.polymorphic_path([:madmin, route_namespace, model], options)
      end

      def new_path
        url_helpers.polymorphic_path([:madmin, route_namespace, model], action: :new)
      end

      def show_path(record)
        url_helpers.polymorphic_path([:madmin, route_namespace, becomes(record)])
      end

      def edit_path(record)
        url_helpers.polymorphic_path([:madmin, route_namespace, becomes(record)], action: :edit)
      end

      def becomes(record)
        record.instance_of?(model) ? record : record.becomes(model)
      end

      def param_key
        model.model_name.param_key
      end

      def permitted_params
        attributes.values.filter { |a| a.field.visible?(:form) }.map { |a| a.field.to_param }
      end

      def display_name(record)
        "#{record.class} ##{record.id}"
      end

      def friendly_model?
        model.respond_to? :friendly
      end

      def sortable_columns
        model.column_names
      end

      def searchable_attributes
        attributes.values.select { |a| a.field.searchable? }
      end

      def member_action(&block)
        member_actions << block
      end

      def field_for_type(type)
        {
          binary: Fields::String,
          blob: Fields::Text,
          boolean: Fields::Boolean,
          currency: Fields::Currency,
          date: Fields::Date,
          datetime: Fields::DateTime,
          decimal: Fields::Decimal,
          enum: Fields::Enum,
          float: Fields::Float,
          hstore: Fields::Json,
          integer: Fields::Integer,
          json: Fields::Json,
          jsonb: Fields::Json,
          primary_key: Fields::String,
          select: Fields::Select,
          string: Fields::String,
          text: Fields::Text,
          time: Fields::Time,
          timestamp: Fields::Time,
          timestamptz: Fields::Time,
          password: Fields::Password,
          file: Fields::File,

          # Postgres specific types
          bit: Fields::String,
          bit_varying: Fields::String,
          box: Fields::String,
          cidr: Fields::String,
          circle: Fields::String,
          citext: Fields::Text,
          daterange: Fields::String,
          inet: Fields::String,
          int4range: Fields::String,
          int8range: Fields::String,
          interval: Fields::String,
          line: Fields::String,
          lseg: Fields::String,
          ltree: Fields::String,
          macaddr: Fields::String,
          money: Fields::String,
          numrange: Fields::String,
          oid: Fields::String,
          path: Fields::String,
          point: Fields::String,
          polygon: Fields::String,
          tsrange: Fields::String,
          tstzrange: Fields::String,
          tsvector: Fields::String,
          uuid: Fields::String,
          xml: Fields::Text,

          # Associations
          attachment: Fields::Attachment,
          attachments: Fields::Attachments,
          belongs_to: Fields::BelongsTo,
          polymorphic: Fields::Polymorphic,
          has_many: Fields::HasMany,
          has_one: Fields::HasOne,
          rich_text: Fields::RichText,
          nested_has_many: Fields::NestedHasMany
        }[type]
      end

      def infer_type(name)
        name_string = name.to_s

        if model.attribute_types.include?(name_string)
          column_type = model.attribute_types[name_string]
          if column_type.is_a? ::ActiveRecord::Enum::EnumType
            :enum
          else
            column_type.type || :string
          end
        elsif (association = model.reflect_on_association(name))
          type_for_association(association)
        elsif model.reflect_on_association(:"rich_text_#{name_string}")
          :rich_text
        elsif model.reflect_on_association(:"#{name_string}_attachment")
          :attachment
        elsif model.reflect_on_association(:"#{name_string}_attachments")
          :attachments

        # has_secure_password
        elsif model.attribute_types.include?("#{name_string}_digest") || name_string.ends_with?("_confirmation")
          :password

          # ActiveRecord Store
        elsif model_store_accessors.include?(name)
          :string
        end
      end

      def type_for_association(association)
        if association.has_one?
          :has_one
        elsif association.collection?
          :has_many
        elsif association.polymorphic?
          :polymorphic
        else
          :belongs_to
        end
      end

      def url_helpers
        @url_helpers ||= Rails.application.routes.url_helpers
      end

      def model_store_accessors
        store_accessors = model.stored_attributes.values
        store_accessors.flatten
      end

      def menu(options)
        @menu_options = options
      end

      def menu_options
        return false if @menu_options == false
        @menu_options ||= {}
        @menu_options.with_defaults(label: friendly_name.pluralize, url: index_path)
      end
    end
  end
end



================================================
FILE: lib/madmin/resource_builder.rb
================================================
module Madmin
  class ResourceBuilder
    attr_reader :model

    def initialize(model)
      @model = model
    end

    def associations
      model.reflections.reject { |name, association|
        # Hide these special associations
        name.starts_with?("rich_text") ||
          name.ends_with?("_attachment") ||
          name.ends_with?("_attachments") ||
          name.ends_with?("_blob") ||
          name.ends_with?("_blobs")
      }.keys
    end

    def attributes
      model.attribute_names + virtual_attributes - redundant_attributes
    end

    def store_accessors
      model.stored_attributes.values.flatten
    end

    def virtual_attributes
      virtual = []

      # has_secure_password columns
      password_attributes = model.attribute_types.keys.select { |k| k.ends_with?("_digest") }.map { |k| k.delete_suffix("_digest") }
      virtual += password_attributes.map { |attr| [attr, "#{attr}_confirmation"] }.flatten

      # ActiveRecord Store columns
      virtual += store_accessors.map(&:to_s)

      # Add virtual attributes for ActionText and ActiveStorage
      model.reflections.each do |name, association|
        if name.starts_with?("rich_text")
          virtual << name.split("rich_text_").last
        elsif name.ends_with?("_attachment")
          virtual << name.split("_attachment").first
        elsif name.ends_with?("_attachments")
          virtual << name.split("_attachments").first
        end
      end

      virtual
    end

    def redundant_attributes
      redundant = []

      # has_secure_password columns
      redundant += model.attribute_types.keys.select { |k| k.ends_with?("_digest") }

      # ActiveRecord Store columns
      store_columns = model.stored_attributes.keys
      redundant += store_columns.map(&:to_s)

      model.reflections.each do |name, association|
        if association.has_one?
          next
        elsif association.collection?
          next
        elsif association.polymorphic?
          redundant << "#{name}_id"
          redundant << "#{name}_type"
        elsif name.starts_with?("rich_text")
          redundant << name
        else # belongs to
          redundant << "#{name}_id"
        end
      end

      redundant
    end
  end
end



================================================
FILE: lib/madmin/search.rb
================================================
# based on Administrate Search: https://github.com/thoughtbot/administrate/blob/main/lib/administrate/search.rb

module Madmin
  class Search
    attr_reader :query

    def initialize(scoped_resource, resource, term)
      @resource = resource
      @scoped_resource = scoped_resource
      @query = term
    end

    def run
      if query.blank?
        @scoped_resource.all
      else
        search_results(@scoped_resource)
      end
    end

    private

    def search_results(resources)
      resources.where(query_template, *query_values)
    end

    def query_template
      search_attributes.map do |attr|
        table_name = query_table_name(attr)
        searchable_fields(attr).map do |field|
          column_name = column_to_query(field)
          "LOWER(CAST(#{table_name}.#{column_name} AS CHAR(256))) LIKE ?"
        end.join(" OR ")
      end.join(" OR ")
    end

    def searchable_fields(attr)
      [attr.name]
    end

    def query_values
      fields_count = search_attributes.sum do |attr|
        searchable_fields(attr).count
      end
      ["%#{@query.mb_chars.downcase}%"] * fields_count
    end

    def search_attributes
      @resource.searchable_attributes
    end

    def query_table_name(attr)
      ::ActiveRecord::Base.connection.quote_column_name(@scoped_resource.table_name)
    end

    def column_to_query(attr)
      ::ActiveRecord::Base.connection.quote_column_name(attr)
    end
  end
end



================================================
FILE: lib/madmin/version.rb
================================================
module Madmin
  VERSION = "2.0.4"
end



================================================
FILE: lib/madmin/view_generator.rb
================================================
require "rails/generators/base"
require "madmin/generator_helpers"
require "madmin/namespace"

module Madmin
  class ViewGenerator < Rails::Generators::Base
    include Madmin::GeneratorHelpers
    class_option :namespace, type: :string, default: "madmin"

    def self.template_source_path
      File.expand_path(
        "../../../app/views/madmin/application",
        __FILE__
      )
    end

    private

    def namespace
      options[:namespace]
    end

    def copy_resource_template(template_name)
      template_file = "#{template_name}.html.erb"

      copy_file(
        template_file,
        "app/views/#{namespace}/#{resource_path}/#{template_file}"
      )
    end

    def resource_path
      args.first.try(:underscore).try(:pluralize) || BaseResourcePath.new
    end

    class BaseResourcePath
      def to_s
        "application"
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/attachment.rb
================================================
module Madmin
  module Fields
    class Attachment < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/attachments.rb
================================================
module Madmin
  module Fields
    class Attachments < Field
      def to_param
        {attribute_name => []}
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/belongs_to.rb
================================================
module Madmin
  module Fields
    class BelongsTo < Field
      def options_for_select(record)
        records = if (record = record.send(attribute_name))
          [record]
        else
          associated_resource.model.first(25)
        end

        records.map { [Madmin.resource_for(_1).display_name(_1), _1.id] }
      end

      def to_param
        "#{attribute_name}_id"
      end

      def index_path
        associated_resource.index_path(format: :json)
      end

      def associated_resource
        Madmin.resource_by_name(model.reflect_on_association(attribute_name).klass)
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/boolean.rb
================================================
module Madmin
  module Fields
    class Boolean < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/currency.rb
================================================
module Madmin
  module Fields
    class Currency < Field
      def value(record)
        value = record.public_send(attribute_name)
        value /= 100.0 if value && options.minor_units
        value
      end

      def searchable?
        options.fetch(:searchable, model.column_names.include?(attribute_name.to_s))
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/date.rb
================================================
module Madmin
  module Fields
    class Date < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/date_time.rb
================================================
module Madmin
  module Fields
    class DateTime < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/decimal.rb
================================================
module Madmin
  module Fields
    class Decimal < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/enum.rb
================================================
module Madmin
  module Fields
    class Enum < Field
      def options_for_select(record)
        model.defined_enums[attribute_name.to_s].keys
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/file.rb
================================================
module Madmin
  module Fields
    class File < Field
      def searchable?
        false
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/float.rb
================================================
module Madmin
  module Fields
    class Float < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/has_many.rb
================================================
module Madmin
  module Fields
    class HasMany < Field
      include Pagy::Backend

      def options_for_select(record)
        if (records = record.send(attribute_name))
          return [] unless records.first
          resource = Madmin.resource_for(records.first)
          records.map { |record| [resource.display_name(record), record.id] }
        else
          []
        end
      end

      def to_param
        {"#{attribute_name.to_s.singularize}_ids": []}
      end

      def index_path
        Madmin.resource_by_name(model.reflect_on_association(attribute_name).klass).index_path(format: :json)
      end

      def paginateable?
        true
      end

      def paginated_value(record, params)
        param_name = "#{attribute_name}_page"
        pagy value(record), page: params[param_name].to_i, page_param: param_name
      rescue Pagy::OverflowError, Pagy::VariableError
        pagy value(record), page: 1, page_param: param_name
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/has_one.rb
================================================
module Madmin
  module Fields
    class HasOne < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/integer.rb
================================================
module Madmin
  module Fields
    class Integer < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/json.rb
================================================
module Madmin
  module Fields
    class Json < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/nested_has_many.rb
================================================
module Madmin
  module Fields
    class NestedHasMany < HasMany
      DEFAULT_ATTRIBUTES = %w[_destroy id].freeze
      def nested_attributes
        resource.attributes.except(*skipped_fields)
      end

      def resource
        "#{to_model.name}Resource".constantize
      end

      def to_param
        {"#{attribute_name}_attributes": permitted_fields}
      end

      def to_partial_path(name)
        unless %w[index show form fields].include? name
          raise ArgumentError, "`partial` must be 'index', 'show', 'form' or 'fields'"
        end

        "/madmin/fields/#{self.class.field_type}/#{name}"
      end

      def to_model
        attribute_name.to_s.singularize.classify.constantize
      end

      def paginateable?
        true
      end

      private

      def permitted_fields
        (resource.permitted_params - skipped_fields + DEFAULT_ATTRIBUTES).uniq
      end

      def skipped_fields
        options[:skip] || []
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/password.rb
================================================
module Madmin
  module Fields
    class Password < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/polymorphic.rb
================================================
module Madmin
  module Fields
    class Polymorphic < Field
      def options_for_select(record)
        if (collection = options[:collection])
          collection.call
        else
          [value(record)].compact
        end
      end

      def to_param
        {attribute_name => %i[type value]}
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/rich_text.rb
================================================
module Madmin
  module Fields
    class RichText < Field
    end
  end
end



================================================
FILE: lib/madmin/fields/select.rb
================================================
module Madmin
  module Fields
    class Select < Field
      def options_for_select(record)
        options.collection
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/string.rb
================================================
module Madmin
  module Fields
    class String < Field
      def searchable?
        options.fetch(:searchable, model.column_names.include?(attribute_name.to_s))
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/text.rb
================================================
module Madmin
  module Fields
    class Text < Field
      def searchable?
        options.fetch(:searchable, model.column_names.include?(attribute_name.to_s))
      end
    end
  end
end



================================================
FILE: lib/madmin/fields/time.rb
================================================
module Madmin
  module Fields
    class Time < Field
    end
  end
end



================================================
FILE: lib/tasks/madmin_tasks.rake
================================================
# desc "Explaining what the task does"
# task :madmin do
#   # Task goes here
# end

namespace :madmin do
  desc "Installs madmin into your app"
  task :install do
    system("#{RbConfig.ruby} ./bin/rails generate madmin:install")
  end
end



================================================
FILE: test/madmin_test.rb
================================================
require "test_helper"

class Madmin::Test < ActiveSupport::TestCase
  test "can find model" do
    assert_equal UserResource.model, User
  end

  test "can find nested model" do
    assert_equal ActionText::RichTextResource.model, ActionText::RichText
    assert_equal User::ConnectedAccountResource.model, User::ConnectedAccount
  end

  test "stores scopes" do
    assert_equal UserResource.scopes, []
  end

  test "stores attributes" do
    assert_instance_of ActiveSupport::OrderedHash, UserResource.attributes
    assert_equal :id, UserResource.attributes.keys.first
  end

  test "can infer attribute type" do
    assert_equal UserResource.send(:infer_type, :id), :integer
    assert_equal UserResource.send(:infer_type, :first_name), :string
    assert_equal UserResource.send(:infer_type, :created_at), :datetime
    assert_equal UserResource.send(:infer_type, :posts), :has_many

    assert_equal UserResource.send(:infer_type, :virtual_attribute), :string

    assert_equal PostResource.send(:infer_type, :body), :rich_text
    assert_equal PostResource.send(:infer_type, :user), :belongs_to
    assert_equal PostResource.send(:infer_type, :image), :attachment
    assert_equal PostResource.send(:infer_type, :attachments), :attachments
    assert_equal PostResource.send(:infer_type, :state), :enum

    assert_equal CommentResource.send(:infer_type, :commentable), :polymorphic
  end

  test "can set custom field for attribute" do
    assert_equal CustomField, PostResource.get_attribute(:title).field.class
  end

  test "has many and nested has many are set to paginateable, others are not" do
    assert UserResource.get_attribute(:posts).field.paginateable?
    assert UserResource.get_attribute(:comments).field.paginateable?
    refute UserResource.get_attribute(:id).field.paginateable?
  end
end



================================================
FILE: test/test_helper.rb
================================================
# Configure Rails Environment
ENV["RAILS_ENV"] = "test"

require_relative "../test/dummy/config/environment"
ActiveRecord::Migrator.migrations_paths = [File.expand_path("../test/dummy/db/migrate", __dir__)]
require "rails/test_help"

# Filter out the backtrace from minitest while preserving the one from other libraries.
Minitest.backtrace_filter = Minitest::BacktraceFilter.new

require "rails/test_unit/reporter"
Rails::TestUnitReporter.executable = "bin/test"

# Load fixtures from the engine
if ActiveSupport::TestCase.respond_to?(:fixture_paths=)
  ActiveSupport::TestCase.fixture_paths = [File.expand_path("fixtures", __dir__)]
  ActionDispatch::IntegrationTest.fixture_paths = ActiveSupport::TestCase.fixture_paths
  ActiveSupport::TestCase.file_fixture_path = File.expand_path("fixtures", __dir__) + "/files"
  ActiveSupport::TestCase.fixtures :all
elsif ActiveSupport::TestCase.respond_to?(:fixture_path=)
  ActiveSupport::TestCase.fixture_path = File.expand_path("fixtures", __dir__)
  ActionDispatch::IntegrationTest.fixture_path = ActiveSupport::TestCase.fixture_path
  ActiveSupport::TestCase.file_fixture_path = ActiveSupport::TestCase.fixture_path + "/files"
  ActiveSupport::TestCase.fixtures :all
end



================================================
FILE: test/dummy/config.ru
================================================
# This file is used by Rack-based servers to start the application.

require_relative "config/environment"

run Rails.application



================================================
FILE: test/dummy/Rakefile
================================================
# Add your own tasks in files placed in lib/tasks ending in .rake,
# for example lib/tasks/capistrano.rake, and they will automatically be available to Rake.

require_relative "config/application"

Rails.application.load_tasks



================================================
FILE: test/dummy/.browserslistrc
================================================
defaults



================================================
FILE: test/dummy/app/assets/config/manifest.js
================================================
//= link_tree ../images
//= link_directory ../stylesheets .css



================================================
FILE: test/dummy/app/assets/images/.keep
================================================



================================================
FILE: test/dummy/app/assets/stylesheets/application.css
================================================
/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, vendor/assets/stylesheets,
 * or any plugin's vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS/SCSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

.trix-content {
  .attachment-gallery {
    > action-text-attachment,
    > .attachment {
      flex: 1 0 33%;
      padding: 0 0.5em;
      max-width: 33%;
    }

    &.attachment-gallery--2,
    &.attachment-gallery--4 {
      > action-text-attachment,
      > .attachment {
        flex-basis: 50%;
        max-width: 50%;
      }
    }
  }

  action-text-attachment {
    .attachment {
      padding: 0 !important;
      max-width: 100% !important;
    }
  }
}




================================================
FILE: test/dummy/app/assets/stylesheets/dummy.css
================================================
/*aside#sidebar h1 a {*/
/*  color: blue;*/
/*}*/



================================================
FILE: test/dummy/app/channels/application_cable/channel.rb
================================================
module ApplicationCable
  class Channel < ActionCable::Channel::Base
  end
end



================================================
FILE: test/dummy/app/channels/application_cable/connection.rb
================================================
module ApplicationCable
  class Connection < ActionCable::Connection::Base
  end
end



================================================
FILE: test/dummy/app/controllers/application_controller.rb
================================================
class ApplicationController < ActionController::Base
end



================================================
FILE: test/dummy/app/controllers/home_controller.rb
================================================
class HomeController < ApplicationController
  def index; end
end



================================================
FILE: test/dummy/app/controllers/concerns/.keep
================================================



================================================
FILE: test/dummy/app/controllers/madmin/application_controller.rb
================================================
module Madmin
  class ApplicationController < Madmin::BaseController
    before_action :authenticate_admin_user

    def authenticate_admin_user
      # TODO: Add your authentication logic here

      # For example, we could redirect if the user isn't an admin
      # redirect_to "/", alert: "Not authorized." unless user_signed_in? && current_user.admin?
    end

    # Authenticate with Clearance
    # include Clearance::Controller
    # before_action :require_login

    # Authenticate with Devise
    # before_action :authenticate_user!

    # Authenticate with Basic Auth
    # http_basic_authenticate_with(name: Rails.application.credentials.admin_username, password: Rails.application.credentials.admin_password)
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/comments_controller.rb
================================================
module Madmin
  class CommentsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/habtms_controller.rb
================================================
module Madmin
  class HabtmsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/numericals_controller.rb
================================================
module Madmin
  class NumericalsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/posts_controller.rb
================================================
module Madmin
  class PostsController < Madmin::ResourceController
    def draft
      @record.update(state: :draft)
      redirect_to main_app.madmin_post_path(@record)
    end

    def publish
      @record.update(state: :published)
      redirect_to main_app.madmin_post_path(@record)
    end

    def archive
      @record.update(state: :archived)
      redirect_to main_app.madmin_post_path(@record)
    end
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/teams_controller.rb
================================================
module Madmin
  class TeamsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/users_controller.rb
================================================
module Madmin
  class UsersController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/action_mailbox/inbound_emails_controller.rb
================================================
module Madmin
  class ActionMailbox::InboundEmailsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/action_text/rich_texts_controller.rb
================================================
module Madmin
  class ActionText::RichTextsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/active_storage/attachments_controller.rb
================================================
module Madmin
  class ActiveStorage::AttachmentsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/active_storage/blobs_controller.rb
================================================
module Madmin
  class ActiveStorage::BlobsController < Madmin::ResourceController
    def new
      super
      @record.assign_attributes(filename: "")
    end
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/active_storage/variant_records_controller.rb
================================================
module Madmin
  class ActiveStorage::VariantRecordsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/paper_trail/versions_controller.rb
================================================
module Madmin
  class PaperTrail::VersionsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/controllers/madmin/user/connected_accounts_controller.rb
================================================
module Madmin
  class User::ConnectedAccountsController < Madmin::ResourceController
  end
end



================================================
FILE: test/dummy/app/helpers/application_helper.rb
================================================
module ApplicationHelper
end



================================================
FILE: test/dummy/app/javascript/packs/application.js
================================================
// This file is automatically compiled by Webpack, along with any other files
// present in this directory. You're encouraged to place your actual application logic in
// a relevant structure within app/javascript and only use these pack files to reference
// that code so it'll be compiled.

require("@rails/ujs").start()
require("turbolinks").start()
require("@rails/activestorage").start()
//require("channels")
require("trix")
require("@rails/actiontext")



================================================
FILE: test/dummy/app/jobs/application_job.rb
================================================
class ApplicationJob < ActiveJob::Base
  # Automatically retry jobs that encountered a deadlock
  # retry_on ActiveRecord::Deadlocked

  # Most jobs are safe to ignore if the underlying records are no longer available
  # discard_on ActiveJob::DeserializationError
end



================================================
FILE: test/dummy/app/madmin/fields/custom_field.rb
================================================
class CustomField < Madmin::Field
  # def value(record)
  #   record.public_send(attribute_name)
  # end

  # def to_partial_path(name)
  #   unless %w[index show form].include? name
  #     raise ArgumentError, "`partial` must be 'index', 'show', or 'form'"
  #  end
  #
  #   "/madmin/fields/#{self.class.field_type}/#{name}"
  # end

  # def to_param
  #   attribute_name
  # end

  # # Used for checking visibility of attribute on an view
  # def visible?(action, default: true)
  #   options.fetch(action.to_sym, default)
  # end

  # def required?
  #   model.validators_on(attribute_name).any? { |v| v.is_a? ActiveModel::Validations::PresenceValidator }
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/comment_resource.rb
================================================
class CommentResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :body
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user
  attribute :commentable, collection: -> { Post.all }

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/habtm_resource.rb
================================================
class HabtmResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name

  # Associations
  attribute :users

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/numerical_resource.rb
================================================
class NumericalResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :decimal
  attribute :float
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/post_resource.rb
================================================
class PostResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :title, field: CustomField
  attribute :comments_count, form: false
  attribute :metadata
  attribute :created_at
  attribute :updated_at, form: false
  attribute :body, index: false
  attribute :image, index: false
  attribute :attachments, index: false
  attribute :state, index: false # Enum example

  attribute :user_id

  # Associations
  attribute :user
  attribute :comments

  # Scopes
  scope :recent

  member_action do
    unless @record.published?
      button_to "Publish", main_app.publish_madmin_post_path(@record), method: :patch, data: { turbo_confirm: "Are you sure?" }, class: "block bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow"
    end
  end


  member_action do
    unless @record.draft?
      button_to "Draft", main_app.draft_madmin_post_path(@record), method: :patch, data: { turbo_confirm: "Are you sure?" }, class: "block bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow"
    end
  end

  member_action do
    unless @record.archived?
      button_to "Archive", main_app.archive_madmin_post_path(@record), method: :patch, data: { turbo_confirm: "Are you sure?" }, class: "block bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow"
    end
  end

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  def self.default_sort_column
    "title"
  end

  def self.default_sort_direction
    "asc"
  end
end



================================================
FILE: test/dummy/app/madmin/resources/user_resource.rb
================================================
class UserResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name, :string, form: :false
  attribute :first_name
  attribute :last_name
  attribute :birthday
  attribute :ssn
  attribute :token, index: false
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :virtual_attribute, index: false
  attribute :password, index: false, show: false
  attribute :password_confirmation, index: false, show: false
  attribute :language
  attribute :notifications
  attribute :weekly_email
  attribute :monthly_newsletter
  attribute :avatar, index: false
  attribute :something, :string, index: false, form: false

  # Associations
  attribute :posts, :nested_has_many, skip: %I[attachments]
  attribute :comments
  attribute :habtms

  # Uncomment this to customize the display name of records in the admin area.
  def self.display_name(record)
    "#{record.first_name} #{record.last_name}"
  end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/action_mailbox/inbound_email_resource.rb
================================================
class ActionMailbox::InboundEmailResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :status
  attribute :message_id
  attribute :message_checksum
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :raw_email, index: false

  # Associations

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/action_text/rich_text_resource.rb
================================================
class ActionText::RichTextResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name
  attribute :body
  attribute :created_at, form: false
  attribute :updated_at, form: false
  attribute :embeds, index: false

  # Associations
  attribute :record

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/active_storage/attachment_resource.rb
================================================
class ActiveStorage::AttachmentResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :name
  attribute :created_at, form: false

  # Associations
  attribute :record
  attribute :blob

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/active_storage/blob_resource.rb
================================================
class ActiveStorage::BlobResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :key
  attribute :filename
  attribute :content_type
  attribute :metadata
  attribute :service_name
  attribute :byte_size
  attribute :checksum
  attribute :created_at, form: false
  attribute :preview_image, index: false

  # Associations
  #attribute :variant_records if Rails.gem_version >= Gem::Version.new("6.1")
  attribute :attachments

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/madmin/resources/user/connected_account_resource.rb
================================================
class User::ConnectedAccountResource < Madmin::Resource
  # Attributes
  attribute :id, form: false
  attribute :service
  attribute :created_at, form: false
  attribute :updated_at, form: false

  # Associations
  attribute :user

  # Uncomment this to customize the display name of records in the admin area.
  # def self.display_name(record)
  #   record.name
  # end

  # Uncomment this to customize the default sort column and direction.
  # def self.default_sort_column
  #   "created_at"
  # end
  #
  # def self.default_sort_direction
  #   "desc"
  # end
end



================================================
FILE: test/dummy/app/mailboxes/application_mailbox.rb
================================================
class ApplicationMailbox < ActionMailbox::Base
  # routing /something/i => :somewhere
end



================================================
FILE: test/dummy/app/mailers/application_mailer.rb
================================================
class ApplicationMailer < ActionMailer::Base
  default from: "<EMAIL>"
  layout "mailer"
end



================================================
FILE: test/dummy/app/models/application_record.rb
================================================
class ApplicationRecord < ActiveRecord::Base
  self.abstract_class = true
end



================================================
FILE: test/dummy/app/models/comment.rb
================================================
class Comment < ApplicationRecord
  belongs_to :user
  belongs_to :commentable, polymorphic: true, counter_cache: true
end



================================================
FILE: test/dummy/app/models/habtm.rb
================================================
class Habtm < ApplicationRecord
  has_and_belongs_to_many :users, join_table: :user_habtms
end



================================================
FILE: test/dummy/app/models/numerical.rb
================================================
class Numerical < ApplicationRecord
end



================================================
FILE: test/dummy/app/models/post.rb
================================================
class Post < ApplicationRecord
  extend FriendlyId
  friendly_id :title

  belongs_to :user
  has_many :comments, as: :commentable, dependent: :destroy
  has_many_attached :attachments
  has_one_attached :image
  has_rich_text :body

  scope :recent, -> { where(created_at: 2.weeks.ago..) }

  enum :state, [:draft, :published, :archived]

  validates :title, presence: true
end



================================================
FILE: test/dummy/app/models/user.rb
================================================
class User < ApplicationRecord
  has_many :connected_accounts, dependent: :destroy
  has_many :posts, dependent: :destroy
  has_many :comments, dependent: :destroy
  has_and_belongs_to_many :habtms, join_table: :user_habtms, dependent: :destroy

  has_one_attached :avatar
  has_person_name
  has_secure_password
  has_secure_token

  if Rails.gem_version >= Gem::Version.new("7.0.0.alpha")
    encrypts :ssn
  end

  store :preferences, accessors: [:language, :notifications], coder: JSON
  store_accessor :settings, [:weekly_email, :monthly_newsletter]

  attribute :virtual_attribute, default: "virtual"

  accepts_nested_attributes_for :posts, allow_destroy: true

  def something
    "Something"
  end
end



================================================
FILE: test/dummy/app/models/concerns/.keep
================================================



================================================
FILE: test/dummy/app/models/user/connected_account.rb
================================================
class User::ConnectedAccount < ApplicationRecord
  belongs_to :user
  validates :service, presence: true
end



================================================
FILE: test/dummy/app/views/active_storage/blobs/_blob.html.erb
================================================
<figure class="attachment attachment--<%= blob.representable? ? "preview" : "file" %> attachment--<%= blob.filename.extension %>">
  <% if blob.representable? %>
    <%= image_tag blob.representation(resize_to_limit: local_assigns[:in_gallery] ? [ 800, 600 ] : [ 1024, 768 ]) %>
  <% end %>

  <figcaption class="attachment__caption">
    <% if caption = blob.try(:caption) %>
      <%= caption %>
    <% else %>
      <span class="attachment__name"><%= blob.filename %></span>
      <span class="attachment__size"><%= number_to_human_size blob.byte_size %></span>
    <% end %>
  </figcaption>
</figure>



================================================
FILE: test/dummy/app/views/home/<USER>
================================================
<h1>There's nothing here, this is a test app for Madmin</h1>
<%= link_to "View Madmin", "/madmin", class: "btn btn-primary" %>



================================================
FILE: test/dummy/app/views/layouts/application.html.erb
================================================
<!DOCTYPE html>
<html>
  <head>
    <title>Dummy</title>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%#= stylesheet_link_tag    'application', media: 'all' %>

    <%# Test Bootstrap 5 with Madmin %>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
  </head>
  <body>
    <%= yield %>
  </body>
</html>



================================================
FILE: test/dummy/app/views/layouts/mailer.html.erb
================================================
<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
      /* Email styles need to be inline */
    </style>
  </head>

  <body>
    <%= yield %>
  </body>
</html>



================================================
FILE: test/dummy/app/views/layouts/mailer.text.erb
================================================
<%= yield %>



================================================
FILE: test/dummy/app/views/madmin/fields/custom_field/_form.html.erb
================================================
<%= form.text_field field.attribute_name, class: "flex-grow form-input" %>



================================================
FILE: test/dummy/app/views/madmin/fields/custom_field/_index.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: test/dummy/app/views/madmin/fields/custom_field/_show.html.erb
================================================
<%= field.value(record) %>



================================================
FILE: test/dummy/config/application.rb
================================================
require_relative "boot"

require "rails/all"

Bundler.require(*Rails.groups)
require "madmin"

module Dummy
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.

    config.action_controller.action_on_unpermitted_parameters = :raise
  end
end



================================================
FILE: test/dummy/config/boot.rb
================================================
# Set up gems listed in the Gemfile.
ENV["BUNDLE_GEMFILE"] ||= File.expand_path("../../../Gemfile", __dir__)

require "bundler/setup" if File.exist?(ENV["BUNDLE_GEMFILE"])
$LOAD_PATH.unshift File.expand_path("../../../lib", __dir__)



================================================
FILE: test/dummy/config/cable.yml
================================================
development:
  adapter: async

test:
  adapter: test

production:
  adapter: redis
  url: <%= ENV.fetch("REDIS_URL") { "redis://localhost:6379/1" } %>
  channel_prefix: dummy_production



================================================
FILE: test/dummy/config/credentials.yml.enc
================================================
miux4BoYRe7S/8JGLUlHtOGl+JyYQrBNQd5dva0/ypRIrsgHGXS3V/YT0Yceqp+Pz4iC40hJRi8gfAc5VTfjlR5nBXTLjz1AvH0GZw8OOvmxQ7c/3rBugyKt7i9zoNHhVTtO1JAJaQcNcIY9o8v+N3maNQEqlD7NU64l+R0vjUeXc7QgIoQYoF90pBQzH6tV7IOGEcQM9+932DXd3M4QXDEn6qMG8/JoE3bSz5bNGWu7raaIWEAqD/9LeDW9/3OYJvJo9eZOYcPvXU5Zjqf5S3bHAFujX6wTKRRt7RJlb9HUh3JIFJcAPCGRyGXiH+iwJ3VdLnTk0ZPTJUeUmM9aA5/OC7CZJSYEGwbkiU8PkpFlSlW6iTshY1ubm8rRHKadEayzXy7Wjqe6CrRD2UM4aQmg7PWkwENV1hM7zlBUO34HGQpQM3Ktxd2w1HwdHOhmkwOL+3jxwQHxibEe7x4Z4KaIRA49e8/bVs0vgDzQRnbww7lHvP2KtUSebdn6slc9/9gBWiKEd0HOQQvk39auM+SNcyuWZX3b9Wfy2mDeyL9VEZCfp624hY6MCKB6YnZdrNu0PjaiPimj7bD1gAj/5nntvYsQ0X6xxh5TefFgNEpf5pCIdKsFPeGNTrQ5mmUUSLnVNzFpWXFpITXDGaY=--IzH/RpiNrz+SKmZ7--bPq+2RYO4SE91LrBw+tnaA==


================================================
FILE: test/dummy/config/database.yml
================================================
# SQLite. Versions 3.8.0 and up are supported.
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem 'sqlite3'
#
default: &default
  adapter: sqlite3
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000

development:
  <<: *default
  database: db/development.sqlite3

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: db/test.sqlite3

production:
  <<: *default
  database: db/production.sqlite3



================================================
FILE: test/dummy/config/environment.rb
================================================
# Load the Rails application.
require_relative "application"

# Initialize the Rails application.
Rails.application.initialize!



================================================
FILE: test/dummy/config/importmap.rb
================================================
pin "@hotwired/turbo-rails", to: "turbo.min.js", preload: true



================================================
FILE: test/dummy/config/master.key
================================================
33c9057eba36d4c26716bb5d916ae19f


================================================
FILE: test/dummy/config/puma.rb
================================================
# Puma can serve each request in a thread from an internal thread pool.
# The `threads` method setting takes two numbers: a minimum and maximum.
# Any libraries that use thread pools should be configured to match
# the maximum value specified for Puma. Default is set to 5 threads for minimum
# and maximum; this matches the default thread size of Active Record.
#
max_threads_count = ENV.fetch("RAILS_MAX_THREADS", 5)
min_threads_count = ENV.fetch("RAILS_MIN_THREADS", max_threads_count)
threads min_threads_count, max_threads_count

# Specifies the `port` that Puma will listen on to receive requests; default is 3000.
#
port ENV.fetch("PORT", 3000)

# Specifies the `environment` that Puma will run in.
#
environment ENV.fetch("RAILS_ENV", "development")

# Specifies the `pidfile` that Puma will use.
pidfile ENV.fetch("PIDFILE", "tmp/pids/server.pid")

# Specifies the number of `workers` to boot in clustered mode.
# Workers are forked web server processes. If using threads and workers together
# the concurrency of the application would be max `threads` * `workers`.
# Workers do not work on JRuby or Windows (both of which do not support
# processes).
#
# workers ENV.fetch("WEB_CONCURRENCY") { 2 }

# Use the `preload_app!` method when specifying a `workers` number.
# This directive tells Puma to first boot the application and load code
# before forking the application. This takes advantage of Copy On Write
# process behavior so workers use less memory.
#
# preload_app!

# Allow puma to be restarted by `rails restart` command.
plugin :tmp_restart



================================================
FILE: test/dummy/config/routes.rb
================================================
Rails.application.routes.draw do
  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html
  root to: "home#index"

  if Gem.loaded_specs["rails"].version >= Gem::Version.new(6.1)
    draw(:madmin)
  else
    route_path = "#{Rails.root}/config/routes/madmin.rb"
    instance_eval(File.read(route_path), route_path.to_s)
  end
end



================================================
FILE: test/dummy/config/spring.rb
================================================
Spring.watch(
  ".ruby-version",
  ".rbenv-vars",
  "tmp/restart.txt",
  "tmp/caching-dev.txt"
)



================================================
FILE: test/dummy/config/storage.yml
================================================
test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

# Use rails credentials:edit to set the AWS secrets (as aws:access_key_id|secret_access_key)
# amazon:
#   service: S3
#   access_key_id: <%= Rails.application.credentials.dig(:aws, :access_key_id) %>
#   secret_access_key: <%= Rails.application.credentials.dig(:aws, :secret_access_key) %>
#   region: us-east-1
#   bucket: your_own_bucket

# Remember not to checkin your GCS keyfile to a repository
# google:
#   service: GCS
#   project: your_project
#   credentials: <%= Rails.root.join("path/to/gcs.keyfile") %>
#   bucket: your_own_bucket

# Use rails credentials:edit to set the Azure Storage secret (as azure_storage:storage_access_key)
# microsoft:
#   service: AzureStorage
#   storage_account_name: your_account_name
#   storage_access_key: <%= Rails.application.credentials.dig(:azure_storage, :storage_access_key) %>
#   container: your_container_name

# mirror:
#   service: Mirror
#   primary: local
#   mirrors: [ amazon, google, microsoft ]



================================================
FILE: test/dummy/config/environments/development.rb
================================================
Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded on
  # every request. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp", "caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    config.cache_store = :memory_store
    config.public_file_server.headers = {
      "Cache-Control" => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  config.action_mailer.perform_caching = false

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Raises error for missing translations.
  # config.action_view.raise_on_missing_translations = true

  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  # config.file_watcher = ActiveSupport::EventedFileUpdateChecker
end



================================================
FILE: test/dummy/config/environments/production.rb
================================================
Rails.application.configure do
  # Prepare the ingress controller used to receive mail
  # config.action_mailbox.ingress = :relay

  # Settings specified here will take precedence over those in config/application.rb.

  # Code is not reloaded between requests.
  config.cache_classes = true

  # Eager load code on boot. This eager loads most of Rails and
  # your application in memory, allowing both threaded web servers
  # and those relying on copy on write to perform better.
  # Rake tasks automatically ignore this option for performance.
  config.eager_load = true

  # Full error reports are disabled and caching is turned on.
  config.consider_all_requests_local = false
  config.action_controller.perform_caching = true

  # Ensures that a master key has been made available in either ENV["RAILS_MASTER_KEY"]
  # or in config/master.key. This key is used to decrypt credentials (and other encrypted files).
  # config.require_master_key = true

  # Disable serving static files from the `/public` folder by default since
  # Apache or NGINX already handles this.
  config.public_file_server.enabled = ENV["RAILS_SERVE_STATIC_FILES"].present?

  # Compress CSS using a preprocessor.
  # config.assets.css_compressor = :sass

  # Do not fallback to assets pipeline if a precompiled asset is missed.
  config.assets.compile = false

  # Enable serving of images, stylesheets, and JavaScripts from an asset server.
  # config.action_controller.asset_host = 'http://assets.example.com'

  # Specifies the header that your server uses for sending files.
  # config.action_dispatch.x_sendfile_header = 'X-Sendfile' # for Apache
  # config.action_dispatch.x_sendfile_header = 'X-Accel-Redirect' # for NGINX

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Mount Action Cable outside main process or domain.
  # config.action_cable.mount_path = nil
  # config.action_cable.url = 'wss://example.com/cable'
  # config.action_cable.allowed_request_origins = [ 'http://example.com', /http:\/\/example.*/ ]

  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  # config.force_ssl = true

  # Use the lowest log level to ensure availability of diagnostic information
  # when problems arise.
  config.log_level = :debug

  # Prepend all log lines with the following tags.
  config.log_tags = [:request_id]

  # Use a different cache store in production.
  # config.cache_store = :mem_cache_store

  # Use a real queuing backend for Active Job (and separate queues per environment).
  # config.active_job.queue_adapter     = :resque
  # config.active_job.queue_name_prefix = "dummy_production"

  config.action_mailer.perform_caching = false

  # Ignore bad email addresses and do not raise email delivery errors.
  # Set this to true and configure the email server for immediate delivery to raise delivery errors.
  # config.action_mailer.raise_delivery_errors = false

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Send deprecation notices to registered listeners.
  config.active_support.deprecation = :notify

  # Use default logging formatter so that PID and timestamp are not suppressed.
  config.log_formatter = ::Logger::Formatter.new

  # Use a different logger for distributed setups.
  # require 'syslog/logger'
  # config.logger = ActiveSupport::TaggedLogging.new(Syslog::Logger.new 'app-name')

  if ENV["RAILS_LOG_TO_STDOUT"].present?
    logger = ActiveSupport::Logger.new($stdout)
    logger.formatter = config.log_formatter
    config.logger = ActiveSupport::TaggedLogging.new(logger)
  end

  # Do not dump schema after migrations.
  config.active_record.dump_schema_after_migration = false

  # Inserts middleware to perform automatic connection switching.
  # The `database_selector` hash is used to pass options to the DatabaseSelector
  # middleware. The `delay` is used to determine how long to wait after a write
  # to send a subsequent read to the primary.
  #
  # The `database_resolver` class is used by the middleware to determine which
  # database is appropriate to use based on the time delay.
  #
  # The `database_resolver_context` class is used by the middleware to set
  # timestamps for the last write to the primary. The resolver uses the context
  # class timestamps to determine how long to wait before reading from the
  # replica.
  #
  # By default Rails will store a last write timestamp in the session. The
  # DatabaseSelector middleware is designed as such you can define your own
  # strategy for connection switching and pass that into the middleware through
  # these configuration options.
  # config.active_record.database_selector = { delay: 2.seconds }
  # config.active_record.database_resolver = ActiveRecord::Middleware::DatabaseSelector::Resolver
  # config.active_record.database_resolver_context = ActiveRecord::Middleware::DatabaseSelector::Resolver::Session
end



================================================
FILE: test/dummy/config/environments/test.rb
================================================
# The test environment is used exclusively to run your application's
# test suite. You never need to work with it otherwise. Remember that
# your test database is "scratch space" for the test suite and is wiped
# and recreated between test runs. Don't rely on the data there!

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  config.cache_classes = false
  config.action_view.cache_template_loading = true

  # Do not eager load code on boot. This avoids loading your whole application
  # just for the purpose of running a single test. If you are using a tool that
  # preloads Rails for running tests, you may have to set it to true.
  config.eager_load = false

  # Configure public file server for tests with Cache-Control for performance.
  config.public_file_server.enabled = true
  config.public_file_server.headers = {
    "Cache-Control" => "public, max-age=#{1.hour.to_i}"
  }

  # Show full error reports and disable caching.
  config.consider_all_requests_local = true
  config.action_controller.perform_caching = false
  config.cache_store = :null_store

  # Raise exceptions instead of rendering exception templates.
  config.action_dispatch.show_exceptions = false

  # Disable request forgery protection in test environment.
  config.action_controller.allow_forgery_protection = false

  # Store uploaded files on the local file system in a temporary directory.
  config.active_storage.service = :test

  config.action_mailer.perform_caching = false

  # Tell Action Mailer not to deliver emails to the real world.
  # The :test delivery method accumulates sent emails in the
  # ActionMailer::Base.deliveries array.
  config.action_mailer.delivery_method = :test

  # Print deprecation notices to the stderr.
  config.active_support.deprecation = :stderr

  # Raises error for missing translations.
  # config.action_view.raise_on_missing_translations = true

  if Rails.gem_version >= Gem::Version.new("7.0.0.alpha")
    config.active_record.encryption.encrypt_fixtures = true
  end
end



================================================
FILE: test/dummy/config/initializers/application_controller_renderer.rb
================================================
# Be sure to restart your server when you modify this file.

# ActiveSupport::Reloader.to_prepare do
#   ApplicationController.renderer.defaults.merge!(
#     http_host: 'example.org',
#     https: false
#   )
# end



================================================
FILE: test/dummy/config/initializers/assets.rb
================================================
# Be sure to restart your server when you modify this file.

if Rails.application.config.respond_to?(:assets)
  # Version of your assets, change this if you want to expire all your assets.
  Rails.application.config.assets.version = '1.0'

  # Add additional assets to the asset load path.
  # Rails.application.config.assets.paths << Emoji.images_path
  # Add Yarn node_modules folder to the asset load path.
  Rails.application.config.assets.paths << Rails.root.join('node_modules')

  # Precompile additional assets.
  # application.js, application.css, and all non-JS/CSS in the app/assets
  # folder are already added.
  # Rails.application.config.assets.precompile += %w( admin.js admin.css )
end



================================================
FILE: test/dummy/config/initializers/backtrace_silencers.rb
================================================
# Be sure to restart your server when you modify this file.

# You can add backtrace silencers for libraries that you're using but don't wish to see in your backtraces.
# Rails.backtrace_cleaner.add_silencer { |line| line =~ /my_noisy_library/ }

# You can also remove all the silencers if you're trying to debug a problem that might stem from framework code.
# Rails.backtrace_cleaner.remove_silencers!



================================================
FILE: test/dummy/config/initializers/content_security_policy.rb
================================================
# Be sure to restart your server when you modify this file.

# Define an application-wide content security policy
# For further information see the following documentation
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy

# Rails.application.config.content_security_policy do |policy|
#   policy.default_src :self, :https
#   policy.font_src    :self, :https, :data
#   policy.img_src     :self, :https, :data
#   policy.object_src  :none
#   policy.script_src  :self, :https
#   policy.style_src   :self, :https

#   # Specify URI for violation reports
#   # policy.report_uri "/csp-violation-report-endpoint"
# end

# If you are using UJS then enable automatic nonce generation
# Rails.application.config.content_security_policy_nonce_generator = -> request { SecureRandom.base64(16) }

# Set the nonce only to specific directives
# Rails.application.config.content_security_policy_nonce_directives = %w(script-src)

# Report CSP violations to a specified URI
# For further information see the following documentation:
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy-Report-Only
# Rails.application.config.content_security_policy_report_only = true



================================================
FILE: test/dummy/config/initializers/cookies_serializer.rb
================================================
# Be sure to restart your server when you modify this file.

# Specify a serializer for the signed and encrypted cookie jars.
# Valid options are :json, :marshal, and :hybrid.
Rails.application.config.action_dispatch.cookies_serializer = :json



================================================
FILE: test/dummy/config/initializers/dummy.rb
================================================
Madmin.stylesheets << "dummy"



================================================
FILE: test/dummy/config/initializers/filter_parameter_logging.rb
================================================
# Be sure to restart your server when you modify this file.

# Configure sensitive parameters which will be filtered from the log file.
Rails.application.config.filter_parameters += [:password]



================================================
FILE: test/dummy/config/initializers/inflections.rb
================================================
# Be sure to restart your server when you modify this file.

# Add new inflection rules using the following format. Inflections
# are locale specific, and you may define rules for as many different
# locales as you wish. All of these examples are active by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.plural /^(ox)$/i, '\1en'
#   inflect.singular /^(ox)en/i, '\1'
#   inflect.irregular 'person', 'people'
#   inflect.uncountable %w( fish sheep )
# end

# These inflection rules are supported but not enabled by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.acronym 'RESTful'
# end



================================================
FILE: test/dummy/config/initializers/mime_types.rb
================================================
# Be sure to restart your server when you modify this file.

# Add new mime types for use in respond_to blocks:
# Mime::Type.register "text/richtext", :rtf



================================================
FILE: test/dummy/config/initializers/wrap_parameters.rb
================================================
# Be sure to restart your server when you modify this file.

# This file contains settings for ActionController::ParamsWrapper which
# is enabled by default.

# Enable parameter wrapping for JSON. You can disable this by setting :format to an empty array.
ActiveSupport.on_load(:action_controller) do
  wrap_parameters format: [:json]
end

# To enable root element in JSON for ActiveRecord objects.
# ActiveSupport.on_load(:active_record) do
#   self.include_root_in_json = true
# end



================================================
FILE: test/dummy/config/locales/en.yml
================================================
# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"



================================================
FILE: test/dummy/config/routes/madmin.rb
================================================
namespace :madmin do
  namespace :paper_trail do
    resources :versions
  end
  namespace :active_storage do
    resources :variant_records
  end
  resources :numericals
  resources :habtms
  resources :teams
  resources :users
  resources :comments
  resources :posts do
    member do
      patch :draft
      patch :publish
      patch :archive
    end
  end
  namespace :action_text do
    resources :rich_texts
  end
  namespace :user do
    resources :connected_accounts
  end
  namespace :active_storage do
    resources :blobs
  end
  namespace :active_storage do
    resources :attachments
  end
  namespace :action_mailbox do
    resources :inbound_emails
  end

  root to: "dashboard#show"
end



================================================
FILE: test/dummy/db/schema.rb
================================================
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2021_09_21_230902) do

  create_table "action_mailbox_inbound_emails", force: :cascade do |t|
    t.bigint "status", default: 0, null: false
    t.string "message_id", null: false
    t.string "message_checksum", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["message_id", "message_checksum"], name: "index_action_mailbox_inbound_emails_uniqueness", unique: true
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "comments", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "commentable_type", null: false
    t.bigint "commentable_id", null: false
    t.text "body"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable_type_and_commentable_id"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "habtms", force: :cascade do |t|
    t.string "name"
  end

  create_table "numericals", force: :cascade do |t|
    t.decimal "decimal"
    t.float "float"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "posts", force: :cascade do |t|
    t.bigint "user_id"
    t.string "title"
    t.integer "comments_count"
    t.integer "state"
    t.json "metadata"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id"], name: "index_posts_on_user_id"
  end

  create_table "user_connected_accounts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "service"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["user_id"], name: "index_user_connected_accounts_on_user_id"
  end

  create_table "user_habtms", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "habtm_id"
    t.index ["habtm_id"], name: "index_user_habtms_on_habtm_id"
    t.index ["user_id"], name: "index_user_habtms_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.date "birthday"
    t.string "password_digest"
    t.string "token"
    t.string "ssn"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.json "settings"
    t.text "preferences"
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object", limit: **********
    t.datetime "created_at"
    t.text "object_changes", limit: **********
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "comments", "users"
  add_foreign_key "user_connected_accounts", "users"
end



================================================
FILE: test/dummy/db/seeds.rb
================================================
100.times do
  user = User.create!(
    first_name: FFaker::Name.first_name,
    last_name: FFaker::Name.last_name,
    birthday: Date.today,
    password: "password"
  )

  # For pagination testing
  100.times do
    Post.create!(
      title: FFaker::Lorem.sentence,
      user:,
    )
  end
end


================================================
FILE: test/dummy/db/migrate/20200911011935_create_users.rb
================================================
class CreateUsers < ActiveRecord::Migration[6.0]
  def change
    create_table :users do |t|
      t.string :first_name
      t.string :last_name
      t.date :birthday
      t.string :password_digest
      t.string :token
      t.string :ssn

      t.timestamps
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911012152_create_posts.rb
================================================
class CreatePosts < ActiveRecord::Migration[6.0]
  def change
    create_table :posts do |t|
      t.belongs_to :user
      t.string :title
      t.integer :comments_count
      t.json :metadata
      t.integer :state

      t.timestamps
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911012214_create_comments.rb
================================================
class CreateComments < ActiveRecord::Migration[6.0]
  def change
    create_table :comments do |t|
      t.belongs_to :user, null: false, foreign_key: true
      t.belongs_to :commentable, polymorphic: true, null: false
      t.text :body

      t.timestamps
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911035531_create_active_storage_tables.active_storage.rb
================================================
# This migration comes from active_storage (originally 20170806125915)
class CreateActiveStorageTables < ActiveRecord::Migration[5.2]
  def change
    create_table :active_storage_blobs do |t|
      t.string   :key,          null: false
      t.string   :filename,     null: false
      t.string   :content_type
      t.text     :metadata
      t.string   :service_name, null: false
      t.bigint   :byte_size,    null: false
      t.string   :checksum,     null: false
      t.datetime :created_at,   null: false

      t.index [ :key ], unique: true
    end

    create_table :active_storage_attachments do |t|
      t.string     :name,     null: false
      t.references :record,   null: false, polymorphic: true, index: false
      t.references :blob,     null: false

      t.datetime :created_at, null: false

      t.index [ :record_type, :record_id, :name, :blob_id ], name: "index_active_storage_attachments_uniqueness", unique: true
      t.foreign_key :active_storage_blobs, column: :blob_id
    end

    create_table :active_storage_variant_records do |t|
      t.belongs_to :blob, null: false, index: false
      t.string :variation_digest, null: false

      t.index %i[ blob_id variation_digest ], name: "index_active_storage_variant_records_uniqueness", unique: true
      t.foreign_key :active_storage_blobs, column: :blob_id
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911035532_create_action_text_tables.action_text.rb
================================================
# This migration comes from action_text (originally 20180528164100)
class CreateActionTextTables < ActiveRecord::Migration[6.0]
  def change
    create_table :action_text_rich_texts do |t|
      t.string :name, null: false
      t.text :body, size: :long
      t.references :record, null: false, polymorphic: true, index: false

      t.timestamps

      t.index [:record_type, :record_id, :name], name: "index_action_text_rich_texts_uniqueness", unique: true
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911145314_create_user_connected_accounts.rb
================================================
class CreateUserConnectedAccounts < ActiveRecord::Migration[6.0]
  def change
    create_table :user_connected_accounts do |t|
      t.belongs_to :user, null: false, foreign_key: true
      t.string :service

      t.timestamps
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20200911160756_create_action_mailbox_tables.action_mailbox.rb
================================================
# This migration comes from action_mailbox (originally **************)
class CreateActionMailboxTables < ActiveRecord::Migration[6.0]
  def change
    create_table :action_mailbox_inbound_emails do |t|
      t.integer :status, default: 0, null: false
      t.string :message_id, null: false
      t.string :message_checksum, null: false

      t.timestamps

      t.index [:message_id, :message_checksum], name: "index_action_mailbox_inbound_emails_uniqueness", unique: true
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20210117173919_add_habtm_model.rb
================================================
class AddHabtmModel < ActiveRecord::Migration[6.1]
  def change
    create_table :user_habtms do |t|
      t.belongs_to :user
      t.belongs_to :habtm
    end

    create_table :habtms do |t|
      t.string :name
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20210117174119_create_numericals.rb
================================================
class CreateNumericals < ActiveRecord::Migration[6.1]
  def change
    create_table :numericals do |t|
      t.decimal :decimal
      t.float :float

      t.timestamps
    end
  end
end



================================================
FILE: test/dummy/db/migrate/20210614192555_create_versions.rb
================================================
# This migration creates the `versions` table, the only schema PT requires.
# All other migrations PT provides are optional.
class CreateVersions < ActiveRecord::Migration[6.1]

  # The largest text column available in all supported RDBMS is
  # 1024^3 - 1 bytes, roughly one gibibyte.  We specify a size
  # so that MySQL will use `longtext` instead of `text`.  Otherwise,
  # when serializing very large objects, `text` might not be big enough.
  TEXT_BYTES = 1_073_741_823

  def change
    create_table :versions do |t|
      t.string   :item_type, null: false
      t.bigint   :item_id,   null: false
      t.string   :event,     null: false
      t.string   :whodunnit
      t.text     :object, limit: TEXT_BYTES

      # Known issue in MySQL: fractional second precision
      # -------------------------------------------------
      #
      # MySQL timestamp columns do not support fractional seconds unless
      # defined with "fractional seconds precision". MySQL users should manually
      # add fractional seconds precision to this migration, specifically, to
      # the `created_at` column.
      # (https://dev.mysql.com/doc/refman/5.6/en/fractional-seconds.html)
      #
      # MySQL users should also upgrade to at least rails 4.2, which is the first
      # version of ActiveRecord with support for fractional seconds in MySQL.
      # (https://github.com/rails/rails/pull/14359)
      #
      t.datetime :created_at
    end
    add_index :versions, %i(item_type item_id)
  end
end



================================================
FILE: test/dummy/db/migrate/20210614192556_add_object_changes_to_versions.rb
================================================
# This migration adds the optional `object_changes` column, in which PaperTrail
# will store the `changes` diff for each update event. See the readme for
# details.
class AddObjectChangesToVersions < ActiveRecord::Migration[6.1]
  # The largest text column available in all supported RDBMS.
  # See `create_versions.rb` for details.
  TEXT_BYTES = 1_073_741_823

  def change
    add_column :versions, :object_changes, :text, limit: TEXT_BYTES
  end
end



================================================
FILE: test/dummy/db/migrate/20210915051634_adds_settings_to_user.rb
================================================
class AddsSettingsToUser < ActiveRecord::Migration[6.1]
  def change
    add_column :users, :settings, :json
  end
end



================================================
FILE: test/dummy/db/migrate/20210915141444_add_preferences_to_users.rb
================================================
class AddPreferencesToUsers < ActiveRecord::Migration[6.1]
  def change
    add_column :users, :preferences, :text
  end
end



================================================
FILE: test/dummy/lib/assets/.keep
================================================



================================================
FILE: test/dummy/log/.keep
================================================



================================================
FILE: test/dummy/public/404.html
================================================
<!DOCTYPE html>
<html>
<head>
  <title>The page you were looking for doesn't exist (404)</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
  .rails-default-error-page {
    background-color: #EFEFEF;
    color: #2E2F30;
    text-align: center;
    font-family: arial, sans-serif;
    margin: 0;
  }

  .rails-default-error-page div.dialog {
    width: 95%;
    max-width: 33em;
    margin: 4em auto 0;
  }

  .rails-default-error-page div.dialog > div {
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #BBB;
    border-top: #B00100 solid 4px;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    background-color: white;
    padding: 7px 12% 0;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }

  .rails-default-error-page h1 {
    font-size: 100%;
    color: #730E15;
    line-height: 1.5em;
  }

  .rails-default-error-page div.dialog > p {
    margin: 0 0 1em;
    padding: 1em;
    background-color: #F7F7F7;
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #999;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-color: #DADADA;
    color: #666;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }
  </style>
</head>

<body class="rails-default-error-page">
  <!-- This file lives in public/404.html -->
  <div class="dialog">
    <div>
      <h1>The page you were looking for doesn't exist.</h1>
      <p>You may have mistyped the address or the page may have moved.</p>
    </div>
    <p>If you are the application owner check the logs for more information.</p>
  </div>
</body>
</html>



================================================
FILE: test/dummy/public/422.html
================================================
<!DOCTYPE html>
<html>
<head>
  <title>The change you wanted was rejected (422)</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
  .rails-default-error-page {
    background-color: #EFEFEF;
    color: #2E2F30;
    text-align: center;
    font-family: arial, sans-serif;
    margin: 0;
  }

  .rails-default-error-page div.dialog {
    width: 95%;
    max-width: 33em;
    margin: 4em auto 0;
  }

  .rails-default-error-page div.dialog > div {
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #BBB;
    border-top: #B00100 solid 4px;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    background-color: white;
    padding: 7px 12% 0;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }

  .rails-default-error-page h1 {
    font-size: 100%;
    color: #730E15;
    line-height: 1.5em;
  }

  .rails-default-error-page div.dialog > p {
    margin: 0 0 1em;
    padding: 1em;
    background-color: #F7F7F7;
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #999;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-color: #DADADA;
    color: #666;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }
  </style>
</head>

<body class="rails-default-error-page">
  <!-- This file lives in public/422.html -->
  <div class="dialog">
    <div>
      <h1>The change you wanted was rejected.</h1>
      <p>Maybe you tried to change something you didn't have access to.</p>
    </div>
    <p>If you are the application owner check the logs for more information.</p>
  </div>
</body>
</html>



================================================
FILE: test/dummy/public/500.html
================================================
<!DOCTYPE html>
<html>
<head>
  <title>We're sorry, but something went wrong (500)</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <style>
  .rails-default-error-page {
    background-color: #EFEFEF;
    color: #2E2F30;
    text-align: center;
    font-family: arial, sans-serif;
    margin: 0;
  }

  .rails-default-error-page div.dialog {
    width: 95%;
    max-width: 33em;
    margin: 4em auto 0;
  }

  .rails-default-error-page div.dialog > div {
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #BBB;
    border-top: #B00100 solid 4px;
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
    background-color: white;
    padding: 7px 12% 0;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }

  .rails-default-error-page h1 {
    font-size: 100%;
    color: #730E15;
    line-height: 1.5em;
  }

  .rails-default-error-page div.dialog > p {
    margin: 0 0 1em;
    padding: 1em;
    background-color: #F7F7F7;
    border: 1px solid #CCC;
    border-right-color: #999;
    border-left-color: #999;
    border-bottom-color: #999;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-color: #DADADA;
    color: #666;
    box-shadow: 0 3px 8px rgba(50, 50, 50, 0.17);
  }
  </style>
</head>

<body class="rails-default-error-page">
  <!-- This file lives in public/500.html -->
  <div class="dialog">
    <div>
      <h1>We're sorry, but something went wrong.</h1>
    </div>
    <p>If you are the application owner check the logs for more information.</p>
  </div>
</body>
</html>



================================================
FILE: test/dummy/test/models/comment_test.rb
================================================
require "test_helper"

class CommentTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end



================================================
FILE: test/dummy/test/models/numerical_test.rb
================================================
require "test_helper"

class NumericalTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end



================================================
FILE: test/dummy/test/models/post_test.rb
================================================
require "test_helper"

class PostTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end



================================================
FILE: test/dummy/test/models/user_test.rb
================================================
require "test_helper"

class UserTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end



================================================
FILE: test/dummy/test/models/user/connected_account_test.rb
================================================
require "test_helper"

class User::ConnectedAccountTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end



================================================
FILE: test/fixtures/comments.yml
================================================
# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  user: one
  commentable: one
  commentable_type: Post
  body: MyText

two:
  user: two
  commentable: two
  commentable_type: Post
  body: MyText



================================================
FILE: test/fixtures/numericals.yml
================================================
# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  decimal: 9.99
  float: 1.5

two:
  decimal: 9.99
  float: 1.5



================================================
FILE: test/fixtures/posts.yml
================================================
# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  title: MyString
  comments_count: 1
  metadata: 

two:
  title: MyString1
  comments_count: 1
  metadata: 



================================================
FILE: test/fixtures/users.yml
================================================
# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  first_name: Chris
  last_name: Oliver
  birthday: 2020-09-10
  password_digest: <%= BCrypt::Password.create('password', cost: 4) %>
  ssn: ***********

two:
  first_name: MyString
  last_name: MyString
  birthday: 2020-09-10
  password_digest: <%= BCrypt::Password.create('password', cost: 4) %>
  ssn: ***********



================================================
FILE: test/fixtures/action_text/rich_texts.yml
================================================
# one:
#   record: name_of_fixture (ClassOfFixture)
#   name: content
#   body: <p>In a <i>million</i> stars!</p>



================================================
FILE: test/fixtures/user/connected_accounts.yml
================================================
# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  user: one
  service: MyString

two:
  user: two
  service: MyString



================================================
FILE: test/integration/users_resource_test.rb
================================================
require "test_helper"

class UsersResourceTest < ActionDispatch::IntegrationTest
  test "can see the users index" do
    get madmin_users_path
    assert_response :success
  end

  test "can see the users new" do
    get new_madmin_user_path
    assert_response :success
  end

  test "can visit new with query params to prefill values" do
    get new_madmin_user_path(user: {first_name: "Chris"})
    assert_select "input[name='user[first_name]'][value=?]", "Chris"
  end

  test "can create user" do
    assert_difference "User.count" do
      post madmin_users_path, params: {user: {first_name: "Updated", password: "password", password_confirmation: "password"}}
      assert_response :redirect
    end
  end

  test "can see the users show" do
    get madmin_user_path(users(:one))
    assert_response :success
  end

  test "can see the users edit" do
    get edit_madmin_user_path(users(:one))
    assert_response :success
  end

  test "can update user" do
    user = users(:one)
    put madmin_user_path(user), params: {user: {first_name: "Updated"}}
    assert_response :redirect
    assert_equal "Updated", user.reload.first_name
  end

  test "can delete user" do
    assert_difference "User.count", -1 do
      delete madmin_user_path(users(:one))
      assert_response :redirect
    end
  end
end



================================================
FILE: test/madmin/field_test.rb
================================================
require "test_helper"

class Madmin::FieldTest < ActiveSupport::TestCase
  test "required?" do
    assert PostResource.attributes[:title].field.required?
    refute PostResource.attributes[:id].field.required?
  end

  test "searchable?" do
    assert UserResource.attributes[:first_name].field.searchable?
    refute UserResource.attributes[:created_at].field.searchable?
  end

  test "visible?" do
    assert UserResource.attributes[:name].field.visible?(:index)
  end
end



================================================
FILE: test/madmin/friendly_id_test.rb
================================================
require "test_helper"

class FriendlyIdTest < ActiveSupport::TestCase
  test "resource can check for friendly_id functionality" do
    assert PostResource.friendly_model?
    refute UserResource.friendly_model?
  end

  test "can find models with friendly_id" do
    post = posts(:one)
    # Make sure we're looking up by something other than the ID
    assert_not_equal post.id, post.to_param
    assert_equal post, PostResource.model_find(post.title)
  end

  test "generates urls with friendly_id slugs" do
    post = posts(:one)
    assert_equal "/madmin/posts/#{post.title}", PostResource.show_path(post)
    assert_equal "/madmin/posts/#{post.title}/edit", PostResource.edit_path(post)
  end
end



================================================
FILE: test/madmin/nested_form_test.rb
================================================
require "test_helper"

class NestedHasManyTest < ActiveSupport::TestCase
  test "checks for the right field class" do
    field = UserResource.attributes[:posts].field
    field_comment = UserResource.attributes[:comments].field

    # Make sure :posts is a :nested_has_many type
    assert field.instance_of?(Madmin::Fields::NestedHasMany)
    refute field_comment.instance_of?(Madmin::Fields::NestedHasMany)
    assert_equal field.resource, PostResource
  end

  test "skips fields which is skipped in configuration" do
    field = UserResource.attributes[:posts].field

    # Make sure :enum is skipped in the UserResource
    refute field.to_param.values.flatten.include?(:enum)
    assert field.to_param.values.flatten.include?(:body)
  end

  test "whitelists unskipped and required params" do
    field = UserResource.attributes[:posts].field
    expected_params = [:title, :metadata, :body, :image, "user_id", "_destroy", "id"]
    assert expected_params.all? { |p| field.to_param[:posts_attributes].include?(p) }
  end
end



================================================
FILE: test/madmin/resource_display_name_test.rb
================================================
require "test_helper"

class ResourceDisplayNameTest < ActiveSupport::TestCase
  test "resource has a custom display name" do
    resource = users(:one)
    assert_equal "Chris Oliver", UserResource.display_name(resource)
  end

  test "resource uses default display name" do
    resource = posts(:one)
    assert_equal "Post ##{resource.id}", PostResource.display_name(resource)
  end
end



================================================
FILE: test/madmin/resource_path_test.rb
================================================
require "test_helper"

class ResourcePathTest < ActiveSupport::TestCase
  test "resource has an index path" do
    assert_equal "/madmin/posts", PostResource.index_path
  end

  test "resource has an index path with query params if given any arguments" do
    assert_equal "/madmin/posts?q=post&t=test", PostResource.index_path(q: "post", t: "test")
  end

  test "resource has a new path" do
    assert_equal "/madmin/posts/new", PostResource.new_path
  end

  test "resource has a show path" do
    post = posts(:one)
    assert_equal "/madmin/posts/#{post.to_param}", PostResource.show_path(post)
  end

  test "resource has an edit path" do
    post = posts(:one)
    assert_equal "/madmin/posts/#{post.to_param}/edit", PostResource.edit_path(post)
  end

  test "resource has an index path for non-model resource" do
    assert_equal "/madmin/action_text/rich_texts", ActionText::RichTextResource.index_path
  end
end



================================================
FILE: test/madmin/resource_test.rb
================================================
require "test_helper"

class FooBarBazResource < Madmin::Resource; end

class ResourceTest < ActiveSupport::TestCase
  test "searchable_attributes" do
    searchable_attribute_names = UserResource.searchable_attributes.map(&:name)
    assert_includes searchable_attribute_names, :first_name
  end

  test "rich_text" do
    assert_equal :rich_text, PostResource.attributes[:body].type
  end

  test "friendly_name" do
    assert_equal "User", UserResource.friendly_name
    assert_equal "Foo Bar Baz", FooBarBazResource.friendly_name
  end
end



================================================
FILE: test/madmin/search_test.rb
================================================
require "test_helper"

class SearchTest < ActiveSupport::TestCase
  test "generates query" do
    results = Madmin::Search.new(User.all, UserResource, "chris").run
    assert_equal users(:one), results.first
  end

  test "returns empty relation when no results found" do
    assert_empty Madmin::Search.new(User.all, UserResource, "nothing").run
  end
end



================================================
FILE: .github/FUNDING.yml
================================================
# These are supported funding model platforms

github: [excid3] # Replace with up to 4 GitHub Sponsors-enabled usernames e.g., [user1, user2]
patreon: # Replace with a single Patreon username
open_collective: # Replace with a single Open Collective username
ko_fi: # Replace with a single Ko-fi username
tidelift: # Replace with a single Tidelift platform-name/package-name e.g., npm/babel
community_bridge: # Replace with a single Community Bridge project-name e.g., cloud-foundry
liberapay: # Replace with a single Liberapay username
issuehunt: # Replace with a single IssueHunt username
otechie: # Replace with a single Otechie username
custom: # Replace with up to 4 custom sponsorship URLs e.g., ['link1', 'link2']



================================================
FILE: .github/workflows/ci.yml
================================================
name: Tests

on:
  pull_request:
    branches:
      - '*'
  push:
    branches:
      - main
  workflow_call:

jobs:
  lint:
    timeout-minutes: 10
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          ruby-version: "3.4"

      - name: StandardRb check
        run: bundle exec standardrb

  sqlite:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby: ['3.2', '3.3', '3.4']
        gemfile:
          - sprockets
          - rails_7
          - rails_7_1
          - rails_7_2
          - rails_8_0
          - rails_main
        exclude:
          # sqlite3 ~> 1.7 is not compatible with Ruby 3.4+
          - gemfile: rails_7
            ruby: '3.4'
          - gemfile: rails_7_1
            ruby: '3.4'
          - gemfile: rails_7_2
            ruby: '3.4'

    env:
      BUNDLE_GEMFILE: ${{ github.workspace }}/gemfiles/${{ matrix.gemfile }}.gemfile
      BUNDLE_PATH_RELATIVE_TO_CWD: true

    steps:
      - uses: actions/checkout@master

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler: default
          bundler-cache: true

      - name: Run tests
        env:
          DATABASE_URL: "sqlite3:noticed_test"
          RAILS_ENV: test
        run: |
          bundle exec rails db:test:prepare
          bundle exec rails test

  mysql:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby: ['3.2', '3.3', '3.4']
        gemfile:
          - sprockets
          - rails_7
          - rails_7_1
          - rails_7_2
          - rails_8_0
          - rails_main
        exclude:
          # sqlite3 ~> 1.7 is not compatible with Ruby 3.4+
          - gemfile: rails_7
            ruby: '3.4'
          - gemfile: rails_7_1
            ruby: '3.4'
          - gemfile: rails_7_2
            ruby: '3.4'

    env:
      BUNDLE_GEMFILE: ${{ github.workspace }}/gemfiles/${{ matrix.gemfile }}.gemfile
      BUNDLE_PATH_RELATIVE_TO_CWD: true

    services:
      mysql:
        image: mysql:8
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test
        ports: ['3306:3306']
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@master

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler: default
          bundler-cache: true

      - name: Run tests
        env:
          DATABASE_URL: mysql2://root:password@127.0.0.1:3306/test
          RAILS_ENV: test
        run: |
          bundle exec rails db:test:prepare
          bundle exec rails test

  postgres:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby: ['3.2', '3.3', '3.4']
        gemfile:
          - sprockets
          - rails_7
          - rails_7_1
          - rails_7_2
          - rails_8_0
          - rails_main
        exclude:
          # sqlite3 ~> 1.7 is not compatible with Ruby 3.4+
          - gemfile: rails_7
            ruby: '3.4'
          - gemfile: rails_7_1
            ruby: '3.4'
          - gemfile: rails_7_2
            ruby: '3.4'

    env:
      BUNDLE_GEMFILE: ${{ github.workspace }}/gemfiles/${{ matrix.gemfile }}.gemfile
      BUNDLE_PATH_RELATIVE_TO_CWD: true

    services:
      postgres:
        image: postgres:12
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test
        ports: ['5432:5432']

    steps:
      - uses: actions/checkout@master

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler: default
          bundler-cache: true

      - name: Run tests
        env:
          DATABASE_URL: postgres://postgres:password@localhost:5432/test
          RAILS_ENV: test
        run: |
          bundle exec rails db:test:prepare
          bundle exec rails test



================================================
FILE: .github/workflows/publish_gem.yml
================================================
name: Publish Gem
on:
  workflow_dispatch:
    inputs:
      version:
        description: "Version"
        required: true
        type: string

jobs:
  test:
    uses: ./.github/workflows/ci.yml

  push:
    needs: test
    runs-on: ubuntu-latest

    permissions:
      contents: write
      id-token: write

    steps:
      # Set up
      - uses: actions/checkout@v4
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          ruby-version: ruby

      - name: Update version
        run: |
          sed -i 's/".*"/"${{ inputs.version }}"/' lib/madmin/version.rb
          bundle config set --local deployment 'false'
          bundle
          bundle exec appraisal
          git config user.name 'GitHub Actions'
          git config user.email <EMAIL>
          git add Gemfile.lock gemfiles lib
          git commit -m "Version bump"
          git push

      # Release
      - uses: rubygems/release-gem@v1


