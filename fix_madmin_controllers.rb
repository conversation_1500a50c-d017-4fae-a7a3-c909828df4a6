#!/usr/bin/env ruby

# Script to fix madmin controller inheritance and resource references

require 'fileutils'

controllers_dir = 'app/controllers/super_admin/madmin'

# Get all controller files except application_controller.rb and users_controller.rb (already correct)
controller_files = Dir.glob("#{controllers_dir}/**/*_controller.rb").reject do |file|
  file.include?('application_controller.rb') || file.include?('users_controller.rb')
end

controller_files.each do |file_path|
  next unless File.exist?(file_path)
  
  content = File.read(file_path)
  
  # Extract controller name from file path
  controller_name = File.basename(file_path, '_controller.rb')

  # Manual singularization for common cases
  singular_name = case controller_name
  when 'conversations' then 'conversation'
  when 'messages' then 'message'
  when 'organizations' then 'organization'
  when 'roles' then 'role'
  when 'sessions' then 'session'
  when 'chat_requests' then 'chat_request'
  when 'job_applications' then 'job_application'
  when 'job_invitations' then 'job_invitation'
  when 'talent_profiles' then 'talent_profile'
  when 'talent_bookmarks' then 'talent_bookmark'
  when 'talent_notes' then 'talent_note'
  when 'user_roles' then 'user_role'
  when 'organization_memberships' then 'organization_membership'
  when 'conversation_participants' then 'conversation_participant'
  when 'impersonation_logs' then 'impersonation_log'
  when 'saved_jobs' then 'saved_job'
  else controller_name.chomp('s')
  end

  resource_name = singular_name.split('_').map(&:capitalize).join + 'Resource'
  
  # Skip pay controllers and other nested controllers for now
  next if file_path.include?('/pay/')
  
  # Create the correct controller content
  new_content = <<~RUBY
    module SuperAdmin
      module Madmin
        class #{controller_name.split('_').map(&:capitalize).join}Controller < ::Madmin::ResourceController
          private

          def resource
            @resource ||= ::#{resource_name}
          end
        end
      end
    end
  RUBY
  
  File.write(file_path, new_content)
  puts "Fixed #{file_path} -> #{resource_name}"
end

puts "Done fixing madmin controllers!"
