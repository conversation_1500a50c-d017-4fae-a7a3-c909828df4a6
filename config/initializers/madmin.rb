# Madmin configuration
Madmin.site_name = 'Ghostwrote Admin'

# Override the default route generation for madmin resources
# to work with our super_admin namespace
module Madmin
  class Resource
    def index_path(params = {})
      Rails.application.routes.url_helpers.send(
        "super_admin_madmin_#{model.model_name.route_key}_path",
        params,
      )
    end

    def show_path(record, params = {})
      Rails.application.routes.url_helpers.send(
        "super_admin_madmin_#{model.model_name.singular_route_key}_path",
        record,
        params,
      )
    end

    def new_path(params = {})
      Rails.application.routes.url_helpers.send(
        "new_super_admin_madmin_#{model.model_name.singular_route_key}_path",
        params,
      )
    end

    def edit_path(record, params = {})
      Rails.application.routes.url_helpers.send(
        "edit_super_admin_madmin_#{model.model_name.singular_route_key}_path",
        record,
        params,
      )
    end
  end
end
